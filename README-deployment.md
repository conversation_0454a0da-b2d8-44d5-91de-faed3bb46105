# 和泰基础资源管理系统前端部署指南

本文档提供了和泰基础资源管理系统前端（ICMS2-Web）的部署说明，包括Docker环境部署和Kubernetes集群部署方法。

## 目录

- [部署方式概述](#部署方式概述)
- [Docker部署](#docker部署)
- [Kubernetes部署](#kubernetes部署)
- [自动化部署脚本](#自动化部署脚本)
- [配置说明](#配置说明)
- [常见问题](#常见问题)

## 部署方式概述

ICMS2-Web前端系统支持以下部署方式：

1. **Docker容器部署**：适用于单机测试或简单环境
2. **Kubernetes集群部署**：适用于生产环境，支持高可用和弹性伸缩

无论采用哪种部署方式，前端都需要与后端API服务进行通信。在生产环境中，建议将前端与后端部署在同一个Kubernetes集群的同一个命名空间下，以便于管理和维护。

## Docker部署

### 快速开始

1. 构建Docker镜像：

```bash
docker build -t icms2-web:latest .
```

2. 运行容器：

```bash
docker run -d -p 8080:80 -e API_URL=http://your-backend-api-url icms2-web:latest
```

3. 访问应用：

浏览器打开 `http://localhost:8080`

### 详细说明

有关Docker部署的详细说明，请参阅[Docker部署指南](docs/docker-deployment.md)。

## Kubernetes部署

### 快速开始

1. 设置环境变量：

```bash
export DOCKER_REGISTRY=<your-registry>
export IMAGE_TAG=<tag>
export NAMESPACE=<namespace>
export INGRESS_HOST=<your-domain>
export API_URL=http://icms2-api-service
```

2. 构建并推送镜像：

```bash
docker build -t ${DOCKER_REGISTRY}/icms2-web:${IMAGE_TAG} .
docker push ${DOCKER_REGISTRY}/icms2-web:${IMAGE_TAG}
```

3. 部署到Kubernetes：

```bash
# 处理配置文件中的变量
for file in k8s/*.yaml; do
  envsubst < $file > .tmp_$(basename $file)
done

# 创建命名空间（如果不存在）
kubectl create namespace ${NAMESPACE} --dry-run=client -o yaml | kubectl apply -f -

# 应用配置
kubectl apply -f .tmp_*.yaml -n ${NAMESPACE}

# 清理临时文件
rm .tmp_*.yaml
```

### 详细说明

有关Kubernetes部署的详细说明，请参阅[Kubernetes部署指南](docs/k8s-deployment-guide.md)。

## 自动化部署脚本

为了简化部署过程，我们提供了自动化部署脚本：

```bash
# 设置环境变量
export DOCKER_REGISTRY=<your-registry>
export IMAGE_TAG=<tag>
export NAMESPACE=<namespace>
export INGRESS_HOST=<your-domain>
export API_URL=http://icms2-api-service

# 执行部署脚本
chmod +x deploy.sh
./deploy.sh
```

脚本会自动检查环境、构建镜像、处理配置文件并部署到Kubernetes集群。

## 配置说明

### 环境变量

部署时需要设置以下环境变量：

| 环境变量 | 说明 | 默认值 |
|---------|------|--------|
| DOCKER_REGISTRY | Docker镜像仓库地址 | localhost:5000 |
| IMAGE_TAG | 镜像标签 | latest |
| NAMESPACE | Kubernetes命名空间 | default |
| INGRESS_HOST | Ingress主机名 | icms2.example.com |
| API_URL | 后端API服务地址 | http://icms2-api-service |

### 配置文件

- `nginx.conf`: Nginx配置文件，包含API代理和单页应用路由处理
- `k8s/deployment.yaml`: Kubernetes部署配置
- `k8s/service.yaml`: Kubernetes服务配置
- `k8s/configmap.yaml`: Kubernetes配置映射
- `k8s/ingress.yaml`: Kubernetes入口配置
- `k8s/kustomization.yaml`: Kustomize配置

## 常见问题

### 1. 前端无法连接后端API

检查以下几点：

- 确认API_URL环境变量设置正确
- 确认后端服务已正常运行
- 检查网络连接和防火墙设置
- 查看Nginx日志是否有代理错误

### 2. 镜像拉取失败

如果使用私有镜像仓库，确保已创建正确的镜像拉取凭证：

```bash
kubectl create secret docker-registry regcred \
  --docker-server=${DOCKER_REGISTRY} \
  --docker-username=<your-username> \
  --docker-password=<your-password> \
  --docker-email=<your-email> \
  --namespace=${NAMESPACE}
```

### 3. Ingress无法访问

检查以下几点：

- 确认Ingress控制器已正确安装
- 检查Ingress资源配置是否正确
- 确认DNS解析已正确配置
- 查看Ingress控制器日志

### 4. 部署后页面空白或报错

检查以下几点：

- 查看浏览器控制台是否有JavaScript错误
- 确认静态资源是否正确加载
- 检查API请求是否正确响应
- 查看Nginx日志是否有错误
