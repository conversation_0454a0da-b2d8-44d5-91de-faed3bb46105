import { useState, useEffect } from 'react';
import { Card, Table, Input, Button, Tag, Space, Tooltip, Modal, Tree } from 'antd';
import { SearchOutlined, EyeOutlined } from '@ant-design/icons';

function UserPermQuery() {
  const [loading, setLoading] = useState(false);
  const [users, setUsers] = useState([]);
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [searchText, setSearchText] = useState('');
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [currentUserDetail, setCurrentUserDetail] = useState(null);
  const [userDetailLoading, setUserDetailLoading] = useState(false);

  // 获取所有用户的权限汇总
  const fetchUsersPermissions = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/users/permissions');
      if (!response.ok) throw new Error('获取用户权限汇总失败');
      const data = await response.json();
      const processedData = data.map((user, index) => ({
        ...user,
        key: `${user.id}-${user.user_type}`,
        index: index + 1
      }));
      setUsers(processedData);
      setFilteredUsers(processedData);
    } catch (error) {
      console.error('获取用户权限汇总错误:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsersPermissions();
  }, []);

  // 处理搜索
  const handleSearch = () => {
    if (!searchText) {
      setFilteredUsers(users);
      return;
    }
    
    const filtered = users.filter(user => 
      user.username.toLowerCase().includes(searchText.toLowerCase()) ||
      (user.display_name && user.display_name.toLowerCase().includes(searchText.toLowerCase()))
    );
    setFilteredUsers(filtered);
  };

  // 重置搜索
  const handleReset = () => {
    setSearchText('');
    setFilteredUsers(users);
  };

  // 查看用户详细权限
  const viewUserDetail = async (userId, userType) => {
    setUserDetailLoading(true);
    setDetailModalVisible(true);
    
    try {
      const response = await fetch(`/api/users/${userId}/permissions?user_type=${userType}`);
      if (!response.ok) throw new Error('获取用户权限详情失败');
      const data = await response.json();
      setCurrentUserDetail(data);
    } catch (error) {
      console.error('获取用户权限详情错误:', error);
    } finally {
      setUserDetailLoading(false);
    }
  };

  // 将模块和菜单数据转换为树形结构
  const convertToTreeData = (modules) => {
    if (!modules) return [];
    
    return modules.map(module => ({
      title: module.name,
      key: `module-${module.id}`,
      children: module.menus?.map(menu => ({
        title: menu.name,
        key: `menu-${menu.id}`,
        isLeaf: true
      })) || []
    }));
  };

  // 列定义
  const columns = [
    { title: '序号', dataIndex: 'index', key: 'index', width: 80 },
    { title: '用户名', dataIndex: 'username', key: 'username' },
    { title: '显示名称', dataIndex: 'display_name', key: 'display_name' },
    { 
      title: '用户类型', 
      dataIndex: 'user_type', 
      key: 'user_type',
      render: (type) => (
        <Tag color={type === 'local' ? 'green' : 'blue'}>
          {type === 'local' ? '本地用户' : 'LDAP用户'}
        </Tag>
      )
    },
    { 
      title: '角色', 
      dataIndex: 'roles', 
      key: 'roles',
      render: (roles) => {
        if (!roles || roles.length === 0) return '无角色';
        return roles.map(role => (
          <Tag color="purple" key={role}>
            {role}
          </Tag>
        ));
      }
    },
    { 
      title: '模块权限', 
      dataIndex: 'modules', 
      key: 'modules',
      render: (modules) => {
        if (!modules || modules.length === 0) return '无权限';
        return (
          <Tooltip title={modules.join(', ')}>
            <span>{modules.length} 个模块</span>
          </Tooltip>
        );
      }
    },
    { 
      title: '菜单权限', 
      dataIndex: 'menus', 
      key: 'menus',
      render: (menus) => {
        if (!menus || menus.length === 0) return '无权限';
        return (
          <Tooltip title={menus.join(', ')}>
            <span>{menus.length} 个菜单</span>
          </Tooltip>
        );
      }
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Button 
          type="primary" 
          icon={<EyeOutlined />} 
          size="small" 
          onClick={() => viewUserDetail(record.id, record.user_type)}
        >
          查看详情
        </Button>
      ),
    },
  ];

  return (
    <Card title="用户权限汇总查询" style={{ maxWidth: 1200, margin: '0 auto' }}>
      <Space style={{ marginBottom: 16 }}>
        <Input
          placeholder="搜索用户名或显示名称"
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          onPressEnter={handleSearch}
          style={{ width: 250 }}
        />
        <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
          搜索
        </Button>
        <Button onClick={handleReset}>重置</Button>
      </Space>
      
      <Table 
        columns={columns} 
        dataSource={filteredUsers} 
        loading={loading}
        bordered
        pagination={{ pageSize: 10 }}
      />
      
      {/* 用户权限详情模态框 */}
      <Modal
        title={`用户权限详情 - ${currentUserDetail?.user?.display_name || currentUserDetail?.user?.username || ''}`}
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={700}
      >
        {userDetailLoading ? (
          <div style={{ textAlign: 'center', padding: '20px 0' }}>加载中...</div>
        ) : (
          <div>
            <div style={{ marginBottom: 16 }}>
              <h4>用户信息</h4>
              <p>用户ID: {currentUserDetail?.user?.id}</p>
              <p>用户名: {currentUserDetail?.user?.username}</p>
              <p>显示名称: {currentUserDetail?.user?.display_name}</p>
            </div>
            
            <div style={{ marginBottom: 16 }}>
              <h4>角色列表</h4>
              {currentUserDetail?.roles?.length > 0 ? (
                currentUserDetail.roles.map(role => (
                  <Tag color="purple" key={role.id}>
                    {role.role_name}
                  </Tag>
                ))
              ) : (
                <p>无角色</p>
              )}
            </div>
            
            <div>
              <h4>权限树</h4>
              {currentUserDetail?.modules?.length > 0 ? (
                <Tree
                  treeData={convertToTreeData(currentUserDetail.modules)}
                  defaultExpandAll
                />
              ) : (
                <p>无权限</p>
              )}
            </div>
          </div>
        )}
      </Modal>
    </Card>
  );
}

export default UserPermQuery;