import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Button,
  Card,
  Table,
  message,
  Space,
  Modal,
  Tooltip,
  Tag,
  Popconfirm,
  Row,
  Col,
  Alert,
  Descriptions,
  Tabs,
  Select,
  Checkbox,
  Spin,
  Empty
} from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
  EditOutlined,
  PoweroffOutlined,
  EyeOutlined,
  LinkOutlined,
  DatabaseOutlined,
  CloudOutlined,
  DesktopOutlined,
  ServerOutlined
} from '@ant-design/icons';
import { formatDate } from '../utils/formatters';

const { TabPane } = Tabs;
const { Option } = Select;
const { CheckboxGroup } = Checkbox;

function AppQuery() {
  const [form] = Form.useForm();
  const [resourceForm] = Form.useForm();
  const [addForm] = Form.useForm();
  const [editForm] = Form.useForm();

  const [loading, setLoading] = useState(false);
  const [appEnvironments, setAppEnvironments] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [error, setError] = useState(null);

  // 详情相关状态
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [currentAppEnv, setCurrentAppEnv] = useState(null);
  const [activeTabKey, setActiveTabKey] = useState('1');
  const [activeResourceTabKey, setActiveResourceTabKey] = useState('lb');
  const [activeSearchTabKey, setActiveSearchTabKey] = useState('1');
  const [resources, setResources] = useState({
    load_balancers: [],
    servers: [],
    databases: []
  });
  const [resourcesLoading, setResourcesLoading] = useState(false);

  // 编辑相关状态
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editLoading, setEditLoading] = useState(false);

  // 新增相关状态
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [addLoading, setAddLoading] = useState(false);
  const [applications, setApplications] = useState([]);
  const [environments, setEnvironments] = useState([]);
  const [deployMethods, setDeployMethods] = useState([]);
  const [applicationsLoading, setApplicationsLoading] = useState(false);
  const [environmentsLoading, setEnvironmentsLoading] = useState(false);
  const [deployMethodsLoading, setDeployMethodsLoading] = useState(false);

  // 资源分配相关状态
  const [resourceModalVisible, setResourceModalVisible] = useState(false);
  const [resourceType, setResourceType] = useState('lb');
  const [availableResources, setAvailableResources] = useState([]);
  const [selectedResource, setSelectedResource] = useState(null);
  const [resourceLoading, setResourceLoading] = useState(false);
  const [serverPort, setServerPort] = useState('');

  // 创建服务器相关状态
  const [createServerModalVisible, setCreateServerModalVisible] = useState(false);
  const [netAreas, setNetAreas] = useState([]);
  const [netAreasLoading, setNetAreasLoading] = useState(false);
  const [createServerLoading, setCreateServerLoading] = useState(false);
  const [selectedNetArea, setSelectedNetArea] = useState(null);
  const [createServerPort, setCreateServerPort] = useState('');

  // 初始化加载
  useEffect(() => {
    fetchAppEnvironments();
  }, []);

  // 获取应用环境列表
  const fetchAppEnvironments = async (params = {}) => {
    setLoading(true);
    setError(null);

    try {
      // 构建查询参数
      const queryParams = new URLSearchParams({
        page: params.page || pagination.current,
        per_page: params.pageSize || pagination.pageSize,
        app_en_name: params.app_en_name || form.getFieldValue('app_en_name') || '',
        app_cn_name: params.app_cn_name || form.getFieldValue('app_cn_name') || '',
      });

      const response = await fetch(`/api/application-environments?${queryParams.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`请求失败，状态码：${response.status}`);
      }

      const data = await response.json();

      // 处理返回数据
      setAppEnvironments(data.application_environments || []);
      setPagination({
        ...pagination,
        current: data.page || 1,
        pageSize: data.per_page || 20,
        total: data.total || 0,
      });

      if (data.application_environments && data.application_environments.length === 0 && data.total > 0) {
        message.info('没有找到匹配的应用环境记录');
      }
    } catch (err) {
      setError(err.message);
      console.error('查询错误:', err);
      message.error(`查询失败：${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 处理表单提交
  const handleSearch = (values) => {
    fetchAppEnvironments({
      ...values,
      page: 1, // 重置到第一页
    });
  };

  // 处理重置
  const handleReset = () => {
    form.resetFields();
    fetchAppEnvironments({ page: 1 });
  };

  // 切换查询方式
  const handleSwitchSearchType = (key) => {
    // 切换到按应用查询
    if (key === '1') {
      form.resetFields();
    }
    // 切换到按资源查询
    else if (key === '2') {
      resourceForm.resetFields();
    }
  };

  // 通过资源查询应用环境
  const handleSearchByResource = (values) => {
    if (!values.resource_type || !values.ip) {
      message.error('请选择资源类型并输入IP地址');
      return;
    }

    setLoading(true);
    setError(null);

    // 获取资源类型的中文名称，用于提示信息
    const resourceTypeText =
      values.resource_type === 'lb' ? '负载均衡' :
      values.resource_type === 'server' ? '服务器' :
      values.resource_type === 'db' ? '数据库' : '资源';

    const queryParams = new URLSearchParams({
      resource_type: values.resource_type,
      ip: values.ip,
      page: 1,
      per_page: pagination.pageSize,
    });

    fetch(`/api/application-environments/search-by-resource?${queryParams.toString()}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })
      .then(response => {
        if (!response.ok) {
          throw new Error(`请求失败，状态码：${response.status}`);
        }
        return response.json();
      })
      .then(data => {
        setAppEnvironments(data.application_environments || []);
        setPagination({
          ...pagination,
          current: data.page || 1,
          pageSize: data.per_page || 20,
          total: data.total || 0,
        });

        if (data.application_environments && data.application_environments.length === 0) {
          message.info(`没有找到IP地址为 ${values.ip} 的${resourceTypeText}关联的应用环境`);
        } else {
          message.success(`找到 ${data.total} 个与${resourceTypeText} (${values.ip}) 关联的应用环境`);
        }
      })
      .catch(err => {
        setError(err.message);
        console.error('查询错误:', err);
        message.error(`查询失败：${err.message}`);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 处理分页变化
  const handleTableChange = (pagination) => {
    fetchAppEnvironments({
      page: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  // 查看应用环境详情
  const handleViewDetail = async (appEnv) => {
    setCurrentAppEnv(appEnv);
    setDetailModalVisible(true);
    setActiveTabKey('1');

    // 获取资源列表
    await fetchAppEnvResources(appEnv.id);
  };

  // 获取应用环境资源列表
  const fetchAppEnvResources = async (appEnvId) => {
    setResourcesLoading(true);
    try {
      const response = await fetch(`/api/application-environments/${appEnvId}/resources`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`获取资源列表失败，状态码：${response.status}`);
      }

      const data = await response.json();
      setResources(data);
    } catch (err) {
      console.error('获取资源列表错误:', err);
      message.error(`获取资源列表失败: ${err.message}`);
    } finally {
      setResourcesLoading(false);
    }
  };

  // 获取网络安全区列表
  const fetchNetAreas = async () => {
    setNetAreasLoading(true);
    try {
      const response = await fetch('/api/cvm/net-areas', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`获取网络安全区列表失败，状态码：${response.status}`);
      }

      const data = await response.json();
      setNetAreas(data.data || []);
    } catch (err) {
      console.error('获取网络安全区列表错误:', err);
      message.error(`获取网络安全区列表失败: ${err.message}`);
    } finally {
      setNetAreasLoading(false);
    }
  };



  // 处理编辑应用环境
  const handleEdit = (appEnv) => {
    editForm.setFieldsValue({
      app_deploy_method: appEnv.app_deploy_method.split(','),
      app_env_url: appEnv.app_env_url,
      app_env_port: appEnv.app_env_port,
      app_env_remark: appEnv.app_env_remark,
    });

    // 获取部署方式下拉列表
    fetchDeployMethods();

    setEditModalVisible(true);
  };

  // 处理下线应用环境
  const handleOffline = async (appEnvId) => {
    try {
      const response = await fetch(`/api/application-environments/${appEnvId}/offline`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (!response.ok) {
        // 检查是否是因为存在已分配资源而无法下线
        if (response.status === 400 && data.error && data.resources) {
          const resourceList = [];
          if (data.resources.load_balancers && data.resources.load_balancers.length > 0) {
            resourceList.push(`负载均衡 ${data.resources.load_balancers.length} 个`);
          }
          if (data.resources.servers && data.resources.servers.length > 0) {
            resourceList.push(`服务器 ${data.resources.servers.length} 个`);
          }
          if (data.resources.databases && data.resources.databases.length > 0) {
            resourceList.push(`数据库 ${data.resources.databases.length} 个`);
          }

          Modal.error({
            title: '无法下线应用环境',
            content: (
              <div>
                <p>{data.error}</p>
                <p>已分配资源：{resourceList.join('、')}</p>
                <p>请先移除所有已分配的资源后再下线应用环境。</p>
              </div>
            ),
            width: 500,
          });
          return;
        } else {
          throw new Error(data.message || data.error || '下线应用环境失败');
        }
      }

      message.success(data.message || '应用环境已成功下线');

      // 关闭详情弹窗
      setDetailModalVisible(false);

      // 刷新列表
      fetchAppEnvironments();
    } catch (err) {
      console.error('下线应用环境错误:', err);
      message.error(`下线应用环境失败: ${err.message}`);
    }
  };

  // 获取部署方式下拉列表
  const fetchDeployMethods = async () => {
    setDeployMethodsLoading(true);
    try {
      const response = await fetch('/api/deploy-methods-dropdown', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`获取部署方式列表失败，状态码：${response.status}`);
      }

      const data = await response.json();
      setDeployMethods(data);
    } catch (err) {
      console.error('获取部署方式列表错误:', err);
      message.error(`获取部署方式列表失败: ${err.message}`);
    } finally {
      setDeployMethodsLoading(false);
    }
  };

  // 获取应用下拉列表
  const fetchApplications = async () => {
    setApplicationsLoading(true);
    try {
      const response = await fetch('/api/applications-dropdown', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`获取应用列表失败，状态码：${response.status}`);
      }

      const data = await response.json();
      setApplications(data);
    } catch (err) {
      console.error('获取应用列表错误:', err);
      message.error(`获取应用列表失败: ${err.message}`);
    } finally {
      setApplicationsLoading(false);
    }
  };

  // 获取环境下拉列表
  const fetchEnvironments = async () => {
    setEnvironmentsLoading(true);
    try {
      const response = await fetch('/api/environments-dropdown', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`获取环境列表失败，状态码：${response.status}`);
      }

      const data = await response.json();
      setEnvironments(data);
    } catch (err) {
      console.error('获取环境列表错误:', err);
      message.error(`获取环境列表失败: ${err.message}`);
    } finally {
      setEnvironmentsLoading(false);
    }
  };

  // 提交编辑表单
  const handleEditSubmit = async () => {
    try {
      const values = await editForm.validateFields();
      setEditLoading(true);

      const response = await fetch(`/api/application-environments/${currentAppEnv.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `更新失败，状态码：${response.status}`);
      }

      const data = await response.json();
      message.success(data.message || '应用环境更新成功');

      // 关闭编辑弹窗
      setEditModalVisible(false);

      // 刷新详情
      const updatedAppEnv = {
        ...currentAppEnv,
        ...values,
      };
      setCurrentAppEnv(updatedAppEnv);

      // 刷新列表
      fetchAppEnvironments();
    } catch (err) {
      console.error('更新应用环境错误:', err);
      message.error(`更新应用环境失败: ${err.message}`);
    } finally {
      setEditLoading(false);
    }
  };

  // 打开新增应用环境弹窗
  const handleShowAddModal = () => {
    addForm.resetFields();

    // 获取下拉列表数据
    fetchApplications();
    fetchEnvironments();
    fetchDeployMethods();

    setAddModalVisible(true);
  };

  // 提交新增表单
  const handleAddSubmit = async () => {
    try {
      const values = await addForm.validateFields();
      setAddLoading(true);

      const response = await fetch('/api/application-environments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `创建失败，状态码：${response.status}`);
      }

      const data = await response.json();
      message.success(data.message || '应用环境创建成功');

      // 关闭新增弹窗
      setAddModalVisible(false);

      // 刷新列表
      fetchAppEnvironments();
    } catch (err) {
      console.error('创建应用环境错误:', err);
      message.error(`创建应用环境失败: ${err.message}`);
    } finally {
      setAddLoading(false);
    }
  };

  // 打开资源分配弹窗
  const handleShowResourceModal = (type) => {
    setResourceType(type);
    setSelectedResource(null);
    setServerPort('');
    setResourceModalVisible(true);
    fetchAvailableResources(type);
  };

  // 处理资源搜索
  const handleResourceSearch = (value) => {
    if (!value.trim()) {
      // 如果搜索关键字为空，不执行搜索
      return;
    }

    // 设置加载状态
    setResourceLoading(true);

    // 执行搜索
    fetchAvailableResources(resourceType, value)
      .then(() => {
        // 搜索完成后，设置一个短暂的延迟，确保资源列表已经更新
        setTimeout(() => {
          // 获取Select组件的DOM元素并模拟点击以展开下拉菜单
          const selectElement = document.querySelector('.resource-select');
          if (selectElement) {
            // 先聚焦到Select组件
            selectElement.focus();
            // 然后模拟点击以展开下拉菜单
            const clickEvent = new MouseEvent('click', {
              view: window,
              bubbles: true,
              cancelable: true
            });
            selectElement.dispatchEvent(clickEvent);
          }
        }, 300);
      });
  };

  // 获取可用资源列表
  const fetchAvailableResources = async (type, keyword = '') => {
    if (!currentAppEnv) return Promise.resolve();

    setResourceLoading(true);
    try {
      let endpoint = '';

      switch (type) {
        case 'lb':
          endpoint = `/api/load-balancers/search?keyword=${encodeURIComponent(keyword)}&limit=1000`;
          break;
        case 'server':
          endpoint = `/api/servers/search?keyword=${encodeURIComponent(keyword)}&limit=1000`;
          break;
        case 'db':
          endpoint = `/api/databases/search?keyword=${encodeURIComponent(keyword)}&limit=1000`;
          break;
        default:
          throw new Error('未知资源类型');
      }

      const response = await fetch(endpoint, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`获取可用资源列表失败，状态码：${response.status}`);
      }

      const data = await response.json();

      let resources = [];

      // 根据资源类型处理返回数据
      if (type === 'lb') {
        resources = data.load_balancers || [];
        setAvailableResources(resources);
      } else if (type === 'server') {
        resources = data.servers || [];
        setAvailableResources(resources);
      } else if (type === 'db') {
        resources = data.databases || [];
        setAvailableResources(resources);
      }

      // 显示搜索结果提示
      if (keyword && resources.length > 0) {
        message.success(`找到 ${resources.length} 个匹配的资源`);
      } else if (keyword && resources.length === 0) {
        message.info('未找到匹配的资源，请尝试其他关键词');
      }

      return Promise.resolve();
    } catch (err) {
      console.error('获取可用资源列表错误:', err);
      message.error(`获取可用资源列表失败: ${err.message}`);
      return Promise.reject(err);
    } finally {
      setResourceLoading(false);
    }
  };

  // 分配资源
  const handleAssignResource = async () => {
    if (!currentAppEnv || !selectedResource) {
      message.error('请选择要分配的资源');
      return;
    }

    setResourceLoading(true);
    try {
      let endpoint = '';
      let body = {};

      switch (resourceType) {
        case 'lb':
          endpoint = `/api/application-environments/${currentAppEnv.id}/load-balancers`;
          body = { lb_id: selectedResource.id };
          break;
        case 'server':
          endpoint = `/api/application-environments/${currentAppEnv.id}/servers`;
          body = {
            server_id: selectedResource.id,
            server_port: serverPort
          };
          break;
        case 'db':
          endpoint = `/api/application-environments/${currentAppEnv.id}/databases`;
          body = { db_id: selectedResource.id };
          break;
        default:
          throw new Error('未知资源类型');
      }

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `资源分配失败，状态码：${response.status}`);
      }

      const data = await response.json();
      message.success(data.message || '资源分配成功');

      // 关闭资源分配弹窗
      setResourceModalVisible(false);

      // 刷新资源列表
      await fetchAppEnvResources(currentAppEnv.id);

      // 切换到对应的资源选项卡
      setActiveTabKey('2'); // 先切换到资源信息选项卡

      // 切换到对应的资源类型选项卡
      setActiveResourceTabKey(resourceType);
    } catch (err) {
      console.error('资源分配错误:', err);
      message.error(`资源分配失败: ${err.message}`);
    } finally {
      setResourceLoading(false);
    }
  };

  // 显示创建服务器模态框
  const handleShowCreateServerModal = () => {
    setSelectedNetArea(null);
    setCreateServerPort('');
    fetchNetAreas();
    setCreateServerModalVisible(true);
  };

  // 创建服务器
  const handleCreateServer = async () => {
    if (!currentAppEnv || !selectedNetArea) {
      message.error('请选择网络安全区');
      return;
    }

    setCreateServerLoading(true);
    try {
      const requestData = {
        application_id: currentAppEnv.application_id,
        environment_id: currentAppEnv.env_id,
        app_en_name: currentAppEnv.app_en_name,
        env_en_name: currentAppEnv.env_en_name,
        net_area_id: selectedNetArea.id,
      };

      // 如果填写了应用端口，则添加到请求数据中
      if (createServerPort.trim()) {
        requestData.server_port = createServerPort.trim();
      }

      const response = await fetch('/api/cvm/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `创建服务器失败，状态码：${response.status}`);
      }

      const data = await response.json();
      message.success(data.message || '服务器创建请求已提交');

      // 关闭创建服务器弹窗
      setCreateServerModalVisible(false);

      // 刷新资源列表
      await fetchAppEnvResources(currentAppEnv.id);

      // 切换到服务器资源选项卡
      setActiveTabKey('2'); // 先切换到资源信息选项卡
      setActiveResourceTabKey('server'); // 切换到服务器选项卡
    } catch (err) {
      console.error('创建服务器错误:', err);
      message.error(`创建服务器失败: ${err.message}`);
    } finally {
      setCreateServerLoading(false);
    }
  };

  // 移除资源
  const handleRemoveResource = async (resourceType, resourceId) => {
    if (!currentAppEnv) return;

    try {
      let endpoint = '';

      switch (resourceType) {
        case 'lb':
          endpoint = `/api/application-environments/${currentAppEnv.id}/load-balancers/${resourceId}`;
          break;
        case 'server':
          endpoint = `/api/application-environments/${currentAppEnv.id}/servers/${resourceId}`;
          break;
        case 'db':
          endpoint = `/api/application-environments/${currentAppEnv.id}/databases/${resourceId}`;
          break;
        default:
          throw new Error('未知资源类型');
      }

      const response = await fetch(endpoint, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `资源移除失败，状态码：${response.status}`);
      }

      const data = await response.json();
      message.success(data.message || '资源移除成功');

      // 刷新资源列表
      fetchAppEnvResources(currentAppEnv.id);
    } catch (err) {
      console.error('资源移除错误:', err);
      message.error(`资源移除失败: ${err.message}`);
    }
  };



  // 表格列定义
  const columns = [
    {
      title: '序号',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      render: (_, __, index) => (pagination.current - 1) * pagination.pageSize + index + 1,
    },
    {
      title: '系统英文名',
      dataIndex: 'app_en_name',
      key: 'app_en_name',
    },
    {
      title: '系统中文名',
      dataIndex: 'app_cn_name',
      key: 'app_cn_name',
    },
    {
      title: '系统环境',
      dataIndex: 'env_cn_name',
      key: 'env_cn_name',
      render: (text, record) => (
        <Tag color={
          record.env_en_name === 'prod' ? 'red' :
          record.env_en_name === 'test' ? 'orange' :
          record.env_en_name === 'dev' ? 'green' : 'blue'
        }>
          {text}
        </Tag>
      ),
    },
    {
      title: '域名',
      dataIndex: 'app_env_url',
      key: 'app_env_url',
      render: (text) => text || '-',
    },
    {
      title: '端口',
      dataIndex: 'app_env_port',
      key: 'app_env_port',
      render: (text) => text || '-',
    },
    {
      title: '状态',
      dataIndex: 'app_env_status',
      key: 'app_env_status',
      render: (status) => (
        <Tag color={status === 'online' ? 'green' : 'red'}>
          {status === 'online' ? '在线' : '下线'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record) => (
        <Button
          type="primary"
          icon={<EyeOutlined />}
          size="small"
          onClick={() => handleViewDetail(record)}
        >
          详情
        </Button>
      ),
    },
  ];

  return (
    <Card
      title="应用查询"
      style={{ minHeight: 400 }}
      extra={
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleShowAddModal}
        >
          新增
        </Button>
      }
    >
      <Tabs activeKey={activeSearchTabKey} onChange={(key) => {
        setActiveSearchTabKey(key);
        handleSwitchSearchType(key);
      }}>
        <TabPane tab="按应用查询" key="1">
          <Form form={form} onFinish={handleSearch} layout="inline" style={{ marginBottom: 16 }}>
            <Form.Item name="app_en_name">
              <Input placeholder="系统英文名" style={{ width: 200 }} />
            </Form.Item>
            <Form.Item name="app_cn_name">
              <Input placeholder="系统中文名" style={{ width: 200 }} />
            </Form.Item>
            <Form.Item>
              <Space>
                <Button type="primary" htmlType="submit" icon={<SearchOutlined />} loading={loading}>
                  查询
                </Button>
                <Button icon={<ReloadOutlined />} onClick={handleReset}>
                  重置
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </TabPane>
        <TabPane tab="按资源查询" key="2">
          <Form form={resourceForm} onFinish={handleSearchByResource} layout="inline" style={{ marginBottom: 16 }}>
            <Form.Item
              name="resource_type"
              rules={[{ required: true, message: '请选择资源类型' }]}
              tooltip="选择要查询的资源类型"
            >
              <Select placeholder="资源类型" style={{ width: 150 }}>
                <Option value="lb">负载均衡</Option>
                <Option value="server">服务器</Option>
                <Option value="db">数据库</Option>
              </Select>
            </Form.Item>
            <Form.Item
              name="ip"
              rules={[{ required: true, message: '请输入IP地址' }]}
              tooltip="输入负载均衡VIP、服务器IP或数据库IP地址"
            >
              <Input placeholder="请输入资源IP地址" style={{ width: 220 }} />
            </Form.Item>
            <Form.Item>
              <Space>
                <Button type="primary" htmlType="submit" icon={<SearchOutlined />} loading={loading}>
                  查询
                </Button>
                <Button icon={<ReloadOutlined />} onClick={() => {
                  resourceForm.resetFields();
                  setActiveSearchTabKey('1'); // 切换回按应用查询
                  fetchAppEnvironments({ page: 1 });
                }}>
                  重置
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </TabPane>
      </Tabs>

      {error && (
        <Alert
          message="查询错误"
          description={error}
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      <Table
        columns={columns}
        dataSource={appEnvironments}
        rowKey="id"
        pagination={{
          ...pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条记录`,
        }}
        loading={loading}
        onChange={handleTableChange}
        bordered
        size="middle"
        scroll={{ x: 1000 }}
      />

      {/* 详情模态框 */}
      <Modal
        title="应用环境详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        width={1000}
        style={{ top: 20 }}
        footer={null}
        styles={{ body: { maxHeight: 'calc(100vh - 200px)', overflowY: 'auto' } }}
      >
        {currentAppEnv ? (
          <Tabs activeKey={activeTabKey} onChange={setActiveTabKey}>
            <TabPane tab="基本信息" key="1">
              <Descriptions bordered column={3} size="middle" labelStyle={{ width: '120px', fontWeight: 'bold' }}>
                <Descriptions.Item label="应用ID" span={1}>{currentAppEnv.application_id}</Descriptions.Item>
                <Descriptions.Item label="环境ID" span={1}>{currentAppEnv.env_id}</Descriptions.Item>
                <Descriptions.Item label="状态" span={1}>
                  <Tag color={currentAppEnv.app_env_status === 'online' ? 'green' : 'red'} style={{ fontSize: '14px', padding: '2px 10px' }}>
                    {currentAppEnv.app_env_status === 'online' ? '在线' : '下线'}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="系统英文名" span={1}>{currentAppEnv.app_en_name}</Descriptions.Item>
                <Descriptions.Item label="系统中文名" span={2}>{currentAppEnv.app_cn_name}</Descriptions.Item>
                <Descriptions.Item label="环境英文名" span={1}>{currentAppEnv.env_en_name}</Descriptions.Item>
                <Descriptions.Item label="环境中文名" span={2}>{currentAppEnv.env_cn_name}</Descriptions.Item>
                <Descriptions.Item label="部署方式" span={3}>
                  {currentAppEnv.deploy_method_names?.map(name => (
                    <Tag key={name} color="blue" style={{ marginRight: '8px' }}>{name}</Tag>
                  )) || '-'}
                </Descriptions.Item>
                <Descriptions.Item label="应用URL" span={3}>
                  {currentAppEnv.app_env_url ? (
                    <a href={currentAppEnv.app_env_url} target="_blank" rel="noopener noreferrer">
                      {currentAppEnv.app_env_url} <LinkOutlined />
                    </a>
                  ) : '-'}
                </Descriptions.Item>
                <Descriptions.Item label="应用端口" span={3}>
                  <span style={{ fontFamily: 'monospace', fontSize: '14px' }}>{currentAppEnv.app_env_port || '-'}</span>
                </Descriptions.Item>
                <Descriptions.Item label="创建时间" span={1}>{formatDate(currentAppEnv.create_time)}</Descriptions.Item>
                <Descriptions.Item label="更新时间" span={1}>{formatDate(currentAppEnv.update_time)}</Descriptions.Item>
                <Descriptions.Item label="下线时间" span={1}>{currentAppEnv.offline_time ? formatDate(currentAppEnv.offline_time) : '-'}</Descriptions.Item>
                <Descriptions.Item label="备注" span={3}>
                  {currentAppEnv.app_env_remark ? (
                    <div style={{ whiteSpace: 'pre-wrap' }}>{currentAppEnv.app_env_remark}</div>
                  ) : '-'}
                </Descriptions.Item>
              </Descriptions>
              <div style={{ marginTop: 24, textAlign: 'center' }}>
                <Space size="large">
                  {currentAppEnv.app_env_status === 'online' ? (
                    <>
                      <Button
                        type="primary"
                        icon={<EditOutlined />}
                        size="large"
                        onClick={() => handleEdit(currentAppEnv)}
                      >
                        编辑应用环境
                      </Button>
                      <Popconfirm
                        title="确定要下线该应用环境吗？"
                        description="下线后将无法再分配资源，请谨慎操作！"
                        onConfirm={() => handleOffline(currentAppEnv.id)}
                        okText="确定"
                        cancelText="取消"
                        placement="topRight"
                      >
                        <Button
                          type="primary"
                          danger
                          icon={<PoweroffOutlined />}
                          size="large"
                        >
                          下线应用环境
                        </Button>
                      </Popconfirm>
                    </>
                  ) : (
                    <Alert
                      message="该应用环境已下线，无法进行编辑和资源分配操作"
                      type="warning"
                      showIcon
                    />
                  )}
                </Space>
              </div>
            </TabPane>
            <TabPane tab="资源信息" key="2">
              <Spin spinning={resourcesLoading}>
                <div style={{ marginBottom: 24, textAlign: 'center' }}>
                  {currentAppEnv?.app_env_status === 'online' ? (
                    <Space size="large" wrap>
                      <Button
                        type="primary"
                        icon={<CloudOutlined />}
                        size="large"
                        onClick={() => handleShowResourceModal('lb')}
                      >
                        分配负载均衡
                      </Button>
                      <Button
                        type="primary"
                        icon={<DesktopOutlined />}
                        size="large"
                        onClick={() => handleShowResourceModal('server')}
                      >
                        分配服务器
                      </Button>
                      <Button
                        type="primary"
                        icon={<DatabaseOutlined />}
                        size="large"
                        onClick={() => handleShowResourceModal('db')}
                      >
                        分配数据库
                      </Button>
                      <Button
                        type="default"
                        icon={<ServerOutlined />}
                        size="large"
                        onClick={handleShowCreateServerModal}
                        style={{ backgroundColor: '#52c41a', borderColor: '#52c41a', color: 'white' }}
                      >
                        创建服务器
                      </Button>
                    </Space>
                  ) : (
                    <Alert
                      message="该应用环境已下线，无法进行资源分配操作"
                      type="warning"
                      showIcon
                    />
                  )}
                </div>

                <Tabs activeKey={activeResourceTabKey} onChange={setActiveResourceTabKey} id="resourceTabs">
                  <TabPane tab="负载均衡" key="lb" data-resource-type="lb">
                    <Table
                      columns={[
                        { title: '序号', dataIndex: 'index', key: 'index', width: 70,
                          render: (_, __, index) => index + 1 },
                        { title: '负载均衡ID', dataIndex: 'loadbalancer_id', key: 'loadbalancer_id', width: 150 },
                        { title: '名称', dataIndex: 'lb_name', key: 'lb_name', width: 180 },
                        { title: 'VIP地址', dataIndex: 'vip', key: 'vip', width: 150,
                          render: (text) => <span style={{ fontFamily: 'monospace' }}>{text}</span> },
                        {
                          title: '操作',
                          key: 'action',
                          fixed: 'right',
                          width: 100,
                          render: (_, record) => (
                            <Popconfirm
                              title="确定要移除该负载均衡资源吗？"
                              onConfirm={() => handleRemoveResource('lb', record.id)}
                              okText="确定"
                              cancelText="取消"
                              disabled={currentAppEnv?.app_env_status !== 'online'}
                            >
                              <Button
                                type="primary"
                                danger
                                size="small"
                                disabled={currentAppEnv?.app_env_status !== 'online'}
                              >
                                移除
                              </Button>
                            </Popconfirm>
                          ),
                        },
                      ]}
                      dataSource={resources.load_balancers}
                      rowKey="id"
                      pagination={false}
                      size="small"
                      bordered
                      scroll={{ x: 650 }}
                      locale={{ emptyText: '暂无负载均衡资源' }}
                    />
                  </TabPane>
                  <TabPane tab="服务器" key="server" data-resource-type="server">
                    <Table
                      columns={[
                        { title: '序号', dataIndex: 'index', key: 'index', width: 70,
                          render: (_, __, index) => index + 1 },
                        { title: '实例ID', dataIndex: 'instance_id', key: 'instance_id', width: 150 },
                        { title: '主机名', dataIndex: 'hostname', key: 'hostname', width: 180 },
                        { title: 'IP地址', dataIndex: 'ipv4', key: 'ipv4', width: 150,
                          render: (text) => <span style={{ fontFamily: 'monospace' }}>{text}</span> },
                        { title: '操作系统', dataIndex: 'os', key: 'os', width: 150 },
                        { title: '端口', dataIndex: 'server_port', key: 'server_port', width: 120,
                          render: (text) => <span style={{ fontFamily: 'monospace' }}>{text}</span> },
                        {
                          title: '操作',
                          key: 'action',
                          fixed: 'right',
                          width: 100,
                          render: (_, record) => (
                            <Popconfirm
                              title="确定要移除该服务器资源吗？"
                              onConfirm={() => handleRemoveResource('server', record.id)}
                              okText="确定"
                              cancelText="取消"
                              disabled={currentAppEnv?.app_env_status !== 'online'}
                            >
                              <Button
                                type="primary"
                                danger
                                size="small"
                                disabled={currentAppEnv?.app_env_status !== 'online'}
                              >
                                移除
                              </Button>
                            </Popconfirm>
                          ),
                        },
                      ]}
                      dataSource={resources.servers}
                      rowKey="id"
                      pagination={false}
                      size="small"
                      bordered
                      scroll={{ x: 800 }}
                      locale={{ emptyText: '暂无服务器资源' }}
                    />
                  </TabPane>
                  <TabPane tab="数据库" key="db" data-resource-type="db">
                    <Table
                      columns={[
                        { title: '序号', dataIndex: 'index', key: 'index', width: 70,
                          render: (_, __, index) => index + 1 },
                        { title: '数据库名称', dataIndex: 'db_name', key: 'db_name', width: 180 },
                        { title: 'IP地址', dataIndex: 'db_ip', key: 'db_ip', width: 150,
                          render: (text) => <span style={{ fontFamily: 'monospace' }}>{text}</span> },
                        { title: '类型', dataIndex: 'db_type', key: 'db_type', width: 120 },
                        {
                          title: '操作',
                          key: 'action',
                          fixed: 'right',
                          width: 100,
                          render: (_, record) => (
                            <Popconfirm
                              title="确定要移除该数据库资源吗？"
                              onConfirm={() => handleRemoveResource('db', record.id)}
                              okText="确定"
                              cancelText="取消"
                              disabled={currentAppEnv?.app_env_status !== 'online'}
                            >
                              <Button
                                type="primary"
                                danger
                                size="small"
                                disabled={currentAppEnv?.app_env_status !== 'online'}
                              >
                                移除
                              </Button>
                            </Popconfirm>
                          ),
                        },
                      ]}
                      dataSource={resources.databases}
                      rowKey="id"
                      pagination={false}
                      size="small"
                      bordered
                      scroll={{ x: 620 }}
                      locale={{ emptyText: '暂无数据库资源' }}
                    />
                  </TabPane>
                </Tabs>
              </Spin>
            </TabPane>
          </Tabs>
        ) : (
          <Empty description="未找到应用环境信息" />
        )}
      </Modal>

      {/* 编辑模态框 */}
      <Modal
        title="编辑应用环境"
        open={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        onOk={handleEditSubmit}
        confirmLoading={editLoading}
        width={600}
      >
        <Form
          form={editForm}
          layout="vertical"
        >
          <Form.Item
            name="app_deploy_method"
            label="部署方式"
            rules={[{ required: true, message: '请选择部署方式' }]}
          >
            <Select
              mode="multiple"
              placeholder="请选择部署方式"
              loading={deployMethodsLoading}
              optionFilterProp="children"
              showSearch
              filterOption={(input, option) =>
                String(option?.children || '').toLowerCase().includes(input.toLowerCase())
              }
            >
              {deployMethods.map(method => (
                <Option key={method.id} value={method.id.toString()}>
                  {method.deploy_name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="app_env_url"
            label="应用URL"
          >
            <Input placeholder="请输入应用URL" />
          </Form.Item>
          <Form.Item
            name="app_env_port"
            label="应用端口"
          >
            <Input placeholder="请输入应用端口" type="number" />
          </Form.Item>
          <Form.Item
            name="app_env_remark"
            label="备注"
          >
            <Input.TextArea placeholder="请输入备注信息" rows={4} />
          </Form.Item>
        </Form>
      </Modal>

      {/* 新增模态框 */}
      <Modal
        title="新增应用环境"
        open={addModalVisible}
        onCancel={() => setAddModalVisible(false)}
        onOk={handleAddSubmit}
        confirmLoading={addLoading}
        width={600}
      >
        <Form
          form={addForm}
          layout="vertical"
        >
          <Form.Item
            name="application_id"
            label="应用系统"
            rules={[{ required: true, message: '请选择应用系统' }]}
          >
            <Select
              placeholder="请选择应用系统"
              loading={applicationsLoading}
              optionFilterProp="children"
              showSearch
              filterOption={(input, option) =>
                String(option?.children || '').toLowerCase().includes(input.toLowerCase())
              }
            >
              {applications.map(app => (
                <Option key={app.id} value={app.id}>
                  {app.app_cn_name} ({app.app_en_name})
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="env_id"
            label="环境"
            rules={[{ required: true, message: '请选择环境' }]}
          >
            <Select
              placeholder="请选择环境"
              loading={environmentsLoading}
              optionFilterProp="children"
              showSearch
              filterOption={(input, option) =>
                String(option?.children || '').toLowerCase().includes(input.toLowerCase())
              }
            >
              {environments.map(env => (
                <Option key={env.id} value={env.id}>
                  {env.env_cn_name} ({env.env_en_name})
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="app_deploy_method"
            label="部署方式"
            rules={[{ required: true, message: '请选择部署方式' }]}
          >
            <Select
              mode="multiple"
              placeholder="请选择部署方式"
              loading={deployMethodsLoading}
              optionFilterProp="children"
              showSearch
              filterOption={(input, option) =>
                String(option?.children || '').toLowerCase().includes(input.toLowerCase())
              }
            >
              {deployMethods.map(method => (
                <Option key={method.id} value={method.id.toString()}>
                  {method.deploy_name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="app_env_url"
            label="应用URL"
          >
            <Input placeholder="请输入应用URL" />
          </Form.Item>
          <Form.Item
            name="app_env_port"
            label="应用端口"
          >
            <Input placeholder="请输入应用端口" type="number" />
          </Form.Item>
          <Form.Item
            name="app_env_remark"
            label="备注"
          >
            <Input.TextArea placeholder="请输入备注信息" rows={4} />
          </Form.Item>
        </Form>
      </Modal>

      {/* 资源分配模态框 */}
      <Modal
        title={`分配${
          resourceType === 'lb' ? '负载均衡' :
          resourceType === 'server' ? '服务器' :
          resourceType === 'db' ? '数据库' : ''
        }资源`}
        open={resourceModalVisible}
        onCancel={() => setResourceModalVisible(false)}
        onOk={handleAssignResource}
        confirmLoading={resourceLoading}
        width={700}
      >
        <Spin spinning={resourceLoading}>
          <Form layout="vertical">
            <Form.Item
              label="搜索资源"
            >
              <Input.Search
                placeholder={`请输入${
                  resourceType === 'lb' ? 'VIP地址、名称或实例ID' :
                  resourceType === 'server' ? 'IP地址、主机名或实例ID' :
                  resourceType === 'db' ? '数据库名称、IP地址或类型' : ''
                }`}
                onSearch={handleResourceSearch}
                enterButton="搜索"
                allowClear
                size="large"
              />
            </Form.Item>
            <Form.Item
              label="选择资源"
              required
              help={availableResources.length > 0 ? `共有 ${availableResources.length} 个可选资源` : '请先搜索资源'}
            >
              <Select
                className="resource-select"
                placeholder={`请选择${
                  resourceType === 'lb' ? '负载均衡' :
                  resourceType === 'server' ? '服务器' :
                  resourceType === 'db' ? '数据库' : ''
                }资源`}
                value={selectedResource?.id}
                onChange={(value) => {
                  const resource = availableResources.find(r => r.id === value);
                  setSelectedResource(resource);
                }}
                style={{ width: '100%' }}
                optionFilterProp="children"
                showSearch
                defaultOpen={false}
                filterOption={(input, option) =>
                  String(option?.children || '').toLowerCase().includes(input.toLowerCase())
                }
              >
                {availableResources.map(resource => (
                  <Option key={resource.id} value={resource.id}>
                    {resourceType === 'lb' ? `${resource.lb_name} (${resource.vip})` :
                     resourceType === 'server' ? `${resource.hostname} (${resource.ipv4})` :
                     resourceType === 'db' ? `${resource.db_name} (${resource.db_ip})` : ''}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            {resourceType === 'server' && (
              <Form.Item
                label="服务器端口"
                extra="多个端口请用逗号分隔，例如：8080,8443"
              >
                <Input
                  placeholder="请输入服务器端口"
                  value={serverPort}
                  onChange={(e) => setServerPort(e.target.value)}
                />
              </Form.Item>
            )}
          </Form>
        </Spin>
      </Modal>

      {/* 创建服务器模态框 */}
      <Modal
        title="创建服务器"
        open={createServerModalVisible}
        onCancel={() => setCreateServerModalVisible(false)}
        onOk={handleCreateServer}
        confirmLoading={createServerLoading}
        width={600}
      >
        <Spin spinning={createServerLoading}>
          <Form layout="vertical">
            <Form.Item
              label="网络安全区"
              required
              help="请选择服务器所在的网络安全区"
            >
              <Select
                placeholder="请选择网络安全区"
                value={selectedNetArea?.id}
                onChange={(value) => {
                  const netArea = netAreas.find(area => area.id === value);
                  setSelectedNetArea(netArea);
                }}
                loading={netAreasLoading}
                style={{ width: '100%' }}
                optionFilterProp="children"
                showSearch
                filterOption={(input, option) =>
                  String(option?.children || '').toLowerCase().includes(input.toLowerCase())
                }
              >
                {netAreas.map(area => (
                  <Option key={area.id} value={area.id}>
                    <div>
                      <div style={{ fontWeight: 'bold' }}>{area.net_area_name}</div>
                      <div style={{ fontSize: '12px', color: '#666' }}>{area.net_area_des}</div>
                    </div>
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              label="应用端口"
              extra="多个端口请用逗号分隔，例如：8080,8443（非必填）"
            >
              <Input
                placeholder="请输入应用端口"
                value={createServerPort}
                onChange={(e) => setCreateServerPort(e.target.value)}
              />
            </Form.Item>
          </Form>
        </Spin>
      </Modal>
    </Card>
  );
}

export default AppQuery;
