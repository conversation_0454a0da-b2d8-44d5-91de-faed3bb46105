import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Button,
  Card,
  Table,
  message,
  Space,
  Modal,
  Tooltip,
  Tag,
  Popconfirm,
  Row,
  Col,
  Alert
} from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
  EditOutlined,
  PoweroffOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { useAuth } from '../contexts/AuthContext';

function SystemManagement() {
  const [form] = Form.useForm();
  const [addForm] = Form.useForm();
  const [editForm] = Form.useForm();

  const [loading, setLoading] = useState(false);
  const [applications, setApplications] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 30,
    total: 0,
  });
  const [error, setError] = useState(null);

  // 模态框状态
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [currentApp, setCurrentApp] = useState(null);

  // 表格列定义
  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 80,
      render: (_, record, index) => (pagination.current - 1) * pagination.pageSize + index + 1,
    },
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '系统英文名',
      dataIndex: 'app_en_name',
      key: 'app_en_name',
      width: 150,
    },
    {
      title: '系统中文名',
      dataIndex: 'app_cn_name',
      key: 'app_cn_name',
      width: 200,
    },
    {
      title: '系统管理员',
      dataIndex: 'app_admin',
      key: 'app_admin',
      width: 120,
    },
    {
      title: '状态',
      dataIndex: 'app_status',
      key: 'app_status',
      width: 100,
      render: (status) => (
        <Tag color={status === 'online' ? 'green' : 'red'}>
          {status === 'online' ? '在线' : '下线'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'create_time',
      key: 'create_time',
      width: 180,
    },
    {
      title: '更新时间',
      dataIndex: 'update_time',
      key: 'update_time',
      width: 180,
    },
    {
      title: '下线时间',
      dataIndex: 'offline_time',
      key: 'offline_time',
      width: 180,
      render: (time) => time || '-',
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      ellipsis: {
        showTitle: false,
      },
      render: (remark) => (
        <Tooltip placement="topLeft" title={remark || '-'}>
          <span>{remark || '-'}</span>
        </Tooltip>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="primary"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            disabled={record.app_status !== 'online'}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要下线该应用系统吗？"
            onConfirm={() => handleOffline(record.id)}
            okText="确定"
            cancelText="取消"
            disabled={record.app_status !== 'online'}
          >
            <Button
              type="default"
              danger
              size="small"
              icon={<PoweroffOutlined />}
              disabled={record.app_status !== 'online'}
            >
              下线
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 初始加载
  useEffect(() => {
    fetchApplications();
  }, []);

  // 获取应用系统列表
  const fetchApplications = async (params = {}) => {
    setLoading(true);
    setError(null);

    try {
      // 构建查询参数
      const queryParams = new URLSearchParams({
        page: params.page || pagination.current,
        per_page: params.pageSize || pagination.pageSize,
        app_en_name: params.app_en_name || form.getFieldValue('app_en_name') || '',
        app_cn_name: params.app_cn_name || form.getFieldValue('app_cn_name') || '',
        app_admin: params.app_admin || form.getFieldValue('app_admin') || '',
      });

      const response = await fetch(`/api/applications?${queryParams.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`请求失败，状态码：${response.status}`);
      }

      const data = await response.json();

      // 处理返回数据
      setApplications(data.applications || []);
      setPagination({
        ...pagination,
        current: data.page || 1,
        pageSize: data.per_page || 30,
        total: data.total || 0,
      });

      if (data.applications && data.applications.length === 0 && data.total > 0) {
        message.info('没有找到匹配的应用系统记录');
      }
    } catch (err) {
      setError(err.message);
      console.error('查询错误:', err);
      message.error(`查询失败：${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 处理表单提交
  const handleSearch = (values) => {
    fetchApplications({
      ...values,
      page: 1, // 重置到第一页
    });
  };

  // 处理重置
  const handleReset = () => {
    form.resetFields();
    fetchApplications({ page: 1 });
  };

  // 处理分页变化
  const handleTableChange = (pagination, filters, sorter) => {
    fetchApplications({
      page: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  // 打开新增应用系统弹窗
  const handleAdd = () => {
    addForm.resetFields();
    setAddModalVisible(true);
  };

  // 提交新增应用系统
  const handleAddSubmit = async () => {
    try {
      const values = await addForm.validateFields();

      const response = await fetch('/api/applications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '创建应用系统失败');
      }

      message.success('应用系统创建成功');
      setAddModalVisible(false);
      fetchApplications(); // 刷新列表
    } catch (err) {
      console.error('创建应用系统错误:', err);
      message.error(err.message || '创建应用系统失败');
    }
  };

  // 打开编辑应用系统弹窗
  const handleEdit = (record) => {
    setCurrentApp(record);
    editForm.setFieldsValue({
      app_admin: record.app_admin,
      remark: record.remark,
    });
    setEditModalVisible(true);
  };

  // 提交编辑应用系统
  const handleEditSubmit = async () => {
    try {
      const values = await editForm.validateFields();

      const response = await fetch(`/api/applications/${currentApp.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || '更新应用系统失败');
      }

      message.success('应用系统更新成功');
      setEditModalVisible(false);
      fetchApplications(); // 刷新列表
    } catch (err) {
      console.error('更新应用系统错误:', err);
      message.error(err.message || '更新应用系统失败');
    }
  };

  // 下线应用系统
  const handleOffline = async (appId) => {
    try {
      const response = await fetch(`/api/applications/${appId}/offline`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (!response.ok) {
        // 检查是否是因为存在在线环境而无法下线
        if (response.status === 400 && data.error && data.environments) {
          const envList = data.environments.map(env => env.env_name).join('、');
          const moreText = data.has_more ? '等' : '';
          Modal.error({
            title: '无法下线应用系统',
            content: (
              <div>
                <p>{data.error}</p>
                <p>在线环境数量：{data.env_count}</p>
                <p>在线环境：{envList}{moreText}</p>
                <p>请先下线所有应用环境后再下线应用系统。</p>
              </div>
            ),
            width: 500,
          });
          return;
        } else {
          throw new Error(data.message || data.error || '下线应用系统失败');
        }
      }

      message.success(data.message || '应用系统已成功下线');
      fetchApplications(); // 刷新列表
    } catch (err) {
      console.error('下线应用系统错误:', err);
      message.error(err.message || '下线应用系统失败');
    }
  };

  return (
    <Card
      title="应用系统管理"
      style={{ minHeight: 400 }}
      extra={
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAdd}
        >
          新增应用系统
        </Button>
      }
    >
      <Form
        form={form}
        name="applicationQueryForm"
        onFinish={handleSearch}
        layout="vertical"
      >
        <Row gutter={16}>
          <Col xs={24} sm={12} md={8}>
            <Form.Item name="app_en_name" label="系统英文名">
              <Input placeholder="输入系统英文名关键字" allowClear />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Form.Item name="app_cn_name" label="系统中文名">
              <Input placeholder="输入系统中文名关键字" allowClear />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Form.Item name="app_admin" label="系统管理员">
              <Input placeholder="输入系统管理员关键字" allowClear />
            </Form.Item>
          </Col>
        </Row>
        <Row>
          <Col span={24} style={{ textAlign: 'right', marginBottom: 16 }}>
            <Space>
              <Button type="primary" htmlType="submit" icon={<SearchOutlined />} loading={loading}>
                查询
              </Button>
              <Button icon={<ReloadOutlined />} onClick={handleReset}>
                重置
              </Button>
            </Space>
          </Col>
        </Row>
      </Form>

      {error && (
        <Alert
          message="查询失败"
          description={`错误信息：${error}`}
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
          closable
          onClose={() => setError(null)}
        />
      )}

      <Table
        columns={columns}
        dataSource={applications.map((app, index) => ({ ...app, key: app.id || index }))}
        pagination={{
          ...pagination,
          showSizeChanger: true,
          showTotal: (total) => `共 ${total} 条记录`,
        }}
        loading={loading}
        onChange={handleTableChange}
        scroll={{ x: 'max-content' }}
        size="middle"
      />

      {/* 新增应用系统弹窗 */}
      <Modal
        title="新增应用系统"
        open={addModalVisible}
        onOk={handleAddSubmit}
        onCancel={() => setAddModalVisible(false)}
        maskClosable={false}
      >
        <Form
          form={addForm}
          layout="vertical"
        >
          <Form.Item
            name="app_en_name"
            label="系统英文名"
            rules={[{ required: true, message: '请输入系统英文名' }]}
          >
            <Input placeholder="请输入系统英文名" />
          </Form.Item>
          <Form.Item
            name="app_cn_name"
            label="系统中文名"
            rules={[{ required: true, message: '请输入系统中文名' }]}
          >
            <Input placeholder="请输入系统中文名" />
          </Form.Item>
          <Form.Item
            name="app_admin"
            label="系统管理员"
            rules={[{ required: true, message: '请输入系统管理员' }]}
          >
            <Input placeholder="请输入系统管理员" />
          </Form.Item>
          <Form.Item
            name="remark"
            label="备注"
          >
            <Input.TextArea placeholder="请输入备注信息" rows={4} />
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑应用系统弹窗 */}
      <Modal
        title="编辑应用系统"
        open={editModalVisible}
        onOk={handleEditSubmit}
        onCancel={() => setEditModalVisible(false)}
        maskClosable={false}
      >
        <Form
          form={editForm}
          layout="vertical"
        >
          <Form.Item
            name="app_admin"
            label="系统管理员"
            rules={[{ required: true, message: '请输入系统管理员' }]}
          >
            <Input placeholder="请输入系统管理员" />
          </Form.Item>
          <Form.Item
            name="remark"
            label="备注"
          >
            <Input.TextArea placeholder="请输入备注信息" rows={4} />
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  );
}

export default SystemManagement;
