import React from 'react';

function App() {
  console.log('App component is rendering - this should appear in console');

  return (
    <div style={{
      padding: '20px',
      background: 'white',
      minHeight: '100vh',
      fontFamily: 'Arial, sans-serif'
    }}>
      <h1 style={{ color: 'red', textAlign: 'center' }}>
        紧急测试页面
      </h1>
      <div style={{ textAlign: 'center', marginTop: '20px' }}>
        <p style={{ fontSize: '18px', color: 'blue' }}>
          如果您能看到这个页面，说明React基本功能正常
        </p>
        <p style={{ fontSize: '14px', color: 'green' }}>
          当前时间: {new Date().toLocaleString()}
        </p>
        <p style={{ fontSize: '14px', color: 'purple' }}>
          React版本: {React.version}
        </p>
      </div>

      <div style={{
        border: '2px solid red',
        padding: '20px',
        margin: '20px auto',
        maxWidth: '600px',
        backgroundColor: '#f9f9f9'
      }}>
        <h2>调试信息</h2>
        <ul style={{ textAlign: 'left' }}>
          <li>✅ React组件正在渲染</li>
          <li>✅ JavaScript正在执行</li>
          <li>✅ CSS样式正在应用</li>
          <li>✅ 浏览器兼容性正常</li>
        </ul>
      </div>

      <div style={{ textAlign: 'center', marginTop: '30px' }}>
        <button
          onClick={() => {
            console.log('Button clicked!');
            alert('按钮功能正常！');
          }}
          style={{
            padding: '15px 30px',
            fontSize: '16px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer'
          }}
        >
          测试按钮交互
        </button>
      </div>
    </div>
  );
}

export default App;

