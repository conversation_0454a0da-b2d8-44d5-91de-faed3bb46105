import React from 'react';
import { AuthProvider } from './contexts/AuthContext';
import AppRoutes from './routes';
import './App.css';

function App() {
  try {
    return (
      <AuthProvider>
        <AppRoutes />
      </AuthProvider>
    );
  } catch (error) {
    console.error('App error:', error);
    return (
      <div style={{ padding: '20px', textAlign: 'center' }}>
        <h2>应用程序出现错误</h2>
        <p>{error.toString()}</p>
        <button onClick={() => window.location.reload()}>刷新页面</button>
      </div>
    );
  }
}

export default App;

