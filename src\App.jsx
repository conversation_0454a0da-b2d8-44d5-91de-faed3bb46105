import React from 'react';
import './App.css';

function App() {
  console.log('App component rendering...');

  return (
    <div style={{ padding: '20px', background: '#f0f2f5', minHeight: '100vh' }}>
      <h1 style={{ textAlign: 'center', marginBottom: '20px' }}>和泰基础资源管理系统</h1>
      <div style={{ textAlign: 'center', marginBottom: '20px' }}>
        <p>当前时间: {new Date().toLocaleString()}</p>
        <p>应用状态: 正在运行</p>
        <p>React版本测试: {React.version}</p>
      </div>

      <div style={{ textAlign: 'center', padding: '40px' }}>
        <h2>系统基础功能测试</h2>
        <p>如果您能看到这个页面，说明React应用基本功能正常</p>
        <div style={{ marginTop: '20px' }}>
          <button
            onClick={() => alert('按钮点击正常')}
            style={{
              display: 'inline-block',
              padding: '10px 20px',
              background: '#1890ff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              marginRight: '10px'
            }}
          >
            测试按钮
          </button>
          <button
            onClick={() => window.location.reload()}
            style={{
              display: 'inline-block',
              padding: '10px 20px',
              background: '#52c41a',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            刷新页面
          </button>
        </div>
        <div style={{ marginTop: '20px', fontSize: '14px', color: '#666' }}>
          <p>如果这个页面正常显示，我们将逐步恢复完整功能</p>
        </div>
      </div>
    </div>
  );
}

export default App;

