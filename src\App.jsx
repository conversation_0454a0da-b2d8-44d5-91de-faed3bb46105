import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import Login from './pages/Login';
import './App.css';

function App() {
  try {
    return (
      <div style={{ padding: '20px' }}>
        <h1>测试页面</h1>
        <AuthProvider>
          <Router>
            <Routes>
              <Route path="/login" element={<Login />} />
              <Route path="*" element={
                <div>
                  <h2>应用正常运行</h2>
                  <p>如果您看到这个页面，说明应用基本功能正常</p>
                  <a href="/login">前往登录页面</a>
                </div>
              } />
            </Routes>
          </Router>
        </AuthProvider>
      </div>
    );
  } catch (error) {
    console.error('App error:', error);
    return (
      <div style={{ padding: '20px', textAlign: 'center' }}>
        <h2>应用程序出现错误</h2>
        <p>{error.toString()}</p>
        <button onClick={() => window.location.reload()}>刷新页面</button>
      </div>
    );
  }
}

export default App;

