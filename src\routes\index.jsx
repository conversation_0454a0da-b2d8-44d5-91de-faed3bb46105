import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import Layout from '../components/Layout';
import Welcome from '../pages/Welcome';
import Login from '../pages/Login';
import AppQuery from '../pages/AppQuery';
import UserQuery from '../pages/UserQuery';
import LdapConfig from '../pages/LdapConfig';
import LdapUserImport from '../pages/LdapUserImport';
import RoleManage from '../pages/RoleManage';
import PermAssign from '../pages/PermAssign';
import UserPermQuery from '../pages/UserPermQuery';
import ServerQuery from '../pages/ServerQuery';
import LoadBalancerQuery from '../pages/LoadBalancerQuery';
import DbQuery from '../pages/DbQuery';
import LogDownload from '../pages/LogDownload';
import SystemManagement from '../pages/SystemManagement';
import ProtectedRoute from './ProtectedRoute';

function AppRoutes() {
  try {
    return (
      <Router>
        <Routes>
          <Route path="/login" element={<Login />} />

          <Route path="/" element={
            <ProtectedRoute path="/">
              <Layout>
                <Welcome />
              </Layout>
            </ProtectedRoute>
          } />

          <Route path="/app-query" element={
            <ProtectedRoute path="/app-query">
              <Layout>
                <AppQuery />
              </Layout>
            </ProtectedRoute>
          } />

          <Route path="/user-query" element={
            <ProtectedRoute path="/user-query">
              <Layout>
                <UserQuery />
              </Layout>
            </ProtectedRoute>
          } />

          <Route path="/ldap-config" element={
            <ProtectedRoute path="/ldap-config">
              <Layout>
                <LdapConfig />
              </Layout>
            </ProtectedRoute>
          } />

          <Route path="/ldap-user-import" element={
            <ProtectedRoute path="/ldap-user-import">
              <Layout>
                <LdapUserImport />
              </Layout>
            </ProtectedRoute>
          } />

          <Route path="/role-manage" element={
            <ProtectedRoute path="/role-manage">
              <Layout>
                <RoleManage />
              </Layout>
            </ProtectedRoute>
          } />

          <Route path="/perm-assign" element={
            <ProtectedRoute path="/perm-assign">
              <Layout>
                <PermAssign />
              </Layout>
            </ProtectedRoute>
          } />

          <Route path="/user-perm-query" element={
            <ProtectedRoute path="/user-perm-query">
              <Layout>
                <UserPermQuery />
              </Layout>
            </ProtectedRoute>
          } />

          <Route path="/host-query" element={
            <ProtectedRoute path="/host-query">
              <Layout>
                <ServerQuery />
              </Layout>
            </ProtectedRoute>
          } />

          <Route path="/load-balancer-query" element={
            <ProtectedRoute path="/load-balancer-query">
              <Layout>
                <LoadBalancerQuery />
              </Layout>
            </ProtectedRoute>
          } />

          <Route path="/db-query" element={
            <ProtectedRoute path="/db-query">
              <Layout>
                <DbQuery />
              </Layout>
            </ProtectedRoute>
          } />

          <Route path="/log-download" element={
            <ProtectedRoute path="/log-download">
              <Layout>
                <LogDownload />
              </Layout>
            </ProtectedRoute>
          } />

          <Route path="/system-management" element={
            <ProtectedRoute path="/system-management">
              <Layout>
                <SystemManagement />
              </Layout>
            </ProtectedRoute>
          } />

          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </Router>
    );
  } catch (error) {
    console.error('AppRoutes error:', error);
    return (
      <div style={{ padding: '20px', textAlign: 'center' }}>
        <h2>路由错误</h2>
        <p>{error.toString()}</p>
        <button onClick={() => window.location.reload()}>刷新页面</button>
      </div>
    );
  }
}

export default AppRoutes;
