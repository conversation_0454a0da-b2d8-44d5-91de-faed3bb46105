import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Button,
  Card,
  Table,
  message,
  Spin,
  Alert,
  Space,
  Tooltip,
  Tag,
  Pagination,
  Row,
  Col,
  Modal,
  Select,
  Popconfirm,
  Descriptions,
  Empty
} from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
  InfoCircleOutlined,
  PlusOutlined,
  DeleteOutlined,
  EyeOutlined
} from '@ant-design/icons';

function DbQuery() {
  const [form] = Form.useForm();
  const [addForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [databases, setDatabases] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 30, // 根据用户需求，每页显示30条记录
    total: 0,
    showSizeChanger: true,
    pageSizeOptions: ['10', '20', '30', '50', '100'],
  });
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [addLoading, setAddLoading] = useState(false);
  const [currentDatabase, setCurrentDatabase] = useState(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);

  // 定义表格列
  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 80,
      render: (_, __, index) => (pagination.current - 1) * pagination.pageSize + index + 1,
    },
    {
      title: '数据库名称',
      dataIndex: 'db_name',
      key: 'db_name',
      ellipsis: true,
    },
    {
      title: 'IP地址',
      dataIndex: 'db_ip',
      key: 'db_ip',
      ellipsis: true,
    },
    {
      title: '类型',
      dataIndex: 'db_type',
      key: 'db_type',
      ellipsis: true,
    },
    {
      title: '用途',
      dataIndex: 'purpose',
      key: 'purpose',
      ellipsis: true,
      render: (text) => text || '-',
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space>
          <Button
            type="primary"
            icon={<EyeOutlined />}
            size="small"
            onClick={() => handleViewDetail(record)}
          >
            详情
          </Button>
          <Popconfirm
            title="确定要下线此数据库吗？"
            onConfirm={() => handleOffline(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              danger
              icon={<DeleteOutlined />}
              size="small"
            >
              下线
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 初始加载
  useEffect(() => {
    fetchDatabases();
  }, []);

  // 获取数据库列表
  const fetchDatabases = async (params = {}) => {
    setLoading(true);
    setError(null);

    try {
      // 构建查询参数
      const queryParams = new URLSearchParams({
        page: params.page || pagination.current,
        per_page: params.pageSize || pagination.pageSize,
        db_name: params.db_name || form.getFieldValue('db_name') || '',
        db_ip: params.db_ip || form.getFieldValue('db_ip') || '',
        db_type: params.db_type || form.getFieldValue('db_type') || '',
      });

      const response = await fetch(`/api/databases?${queryParams.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`请求失败，状态码：${response.status}`);
      }

      const data = await response.json();

      // 处理返回数据
      setDatabases(data.databases || []);
      setPagination({
        ...pagination,
        current: data.page || 1,
        pageSize: data.per_page || 30,
        total: data.total || 0,
      });

      if (data.databases && data.databases.length === 0 && data.total > 0) {
        message.info('没有找到匹配的数据库记录');
      }
    } catch (err) {
      setError(err.message);
      console.error('查询错误:', err);
      message.error(`查询失败：${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 处理表单提交
  const handleSearch = (values) => {
    fetchDatabases({
      ...values,
      page: 1, // 重置到第一页
    });
  };

  // 处理重置
  const handleReset = () => {
    form.resetFields();
    fetchDatabases({ page: 1 });
  };

  // 处理分页变化
  const handleTableChange = (pagination, filters, sorter) => {
    fetchDatabases({
      page: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  // 打开新增数据库弹窗
  const showAddModal = () => {
    addForm.resetFields();
    setAddModalVisible(true);
  };

  // 关闭新增数据库弹窗
  const handleAddCancel = () => {
    setAddModalVisible(false);
  };

  // 提交新增数据库表单
  const handleAddSubmit = async () => {
    try {
      const values = await addForm.validateFields();
      setAddLoading(true);

      try {
        const response = await fetch('/api/databases', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(values),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || '创建数据库失败');
        }

        const data = await response.json();
        message.success('数据库创建成功');
        setAddModalVisible(false);
        fetchDatabases(); // 刷新列表
      } catch (err) {
        console.error('创建数据库错误:', err);
        message.error(`创建数据库失败: ${err.message}`);
      } finally {
        setAddLoading(false);
      }
    } catch (validationError) {
      console.log('表单验证失败:', validationError);
    }
  };

  // 下线数据库
  const handleOffline = async (dbId) => {
    try {
      const response = await fetch(`/api/databases/${dbId}/offline`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({}), // 发送空的JSON对象，确保Content-Type生效
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || errorData.message || '下线数据库失败');
      }

      const data = await response.json();
      message.success('数据库已成功下线');
      fetchDatabases(); // 刷新列表
    } catch (err) {
      console.error('下线数据库错误:', err);
      message.error(`下线数据库失败: ${err.message}`);
    }
  };

  // 查看数据库详情
  const handleViewDetail = (database) => {
    setCurrentDatabase(database);
    setDetailModalVisible(true);
  };

  return (
    <Card
      title="数据库资源查询"
      style={{ minHeight: 400 }}
      extra={
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={showAddModal}
        >
          新增
        </Button>
      }
    >
      <Form
        form={form}
        name="databaseQueryForm"
        onFinish={handleSearch}
        layout="vertical"
      >
        <Row gutter={16}>
          <Col xs={24} sm={12} md={8}>
            <Form.Item name="db_name" label="数据库名称">
              <Input placeholder="输入数据库名称关键字" allowClear />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Form.Item name="db_ip" label="IP地址">
              <Input placeholder="输入IP地址关键字" allowClear />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Form.Item name="db_type" label="数据库类型">
              <Input placeholder="输入数据库类型关键字" allowClear />
            </Form.Item>
          </Col>
        </Row>
        <Row>
          <Col span={24} style={{ textAlign: 'right', marginBottom: 16 }}>
            <Space>
              <Button type="primary" htmlType="submit" icon={<SearchOutlined />} loading={loading}>
                查询
              </Button>
              <Button icon={<ReloadOutlined />} onClick={handleReset}>
                重置
              </Button>
            </Space>
          </Col>
        </Row>
      </Form>

      {error && (
        <Alert
          message="查询失败"
          description={`错误信息：${error}`}
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
          closable
          onClose={() => setError(null)}
        />
      )}

      <Table
        columns={columns}
        dataSource={databases.map((db, index) => ({ ...db, key: db.id || index }))}
        pagination={false}
        loading={loading}
        onChange={handleTableChange}
        scroll={{ x: 'max-content' }}
        size="middle"
      />

      <div style={{ marginTop: 16, textAlign: 'right' }}>
        <Pagination
          current={pagination.current}
          pageSize={pagination.pageSize}
          total={pagination.total}
          showSizeChanger
          showQuickJumper
          showTotal={(total) => `共 ${total} 条记录`}
          onChange={(page, pageSize) => fetchDatabases({ page, pageSize })}
          onShowSizeChange={(current, size) => fetchDatabases({ page: 1, pageSize: size })}
        />
      </div>

      {/* 新增数据库弹窗 */}
      <Modal
        title="新增数据库"
        open={addModalVisible}
        onCancel={handleAddCancel}
        footer={[
          <Button key="cancel" onClick={handleAddCancel}>
            取消
          </Button>,
          <Button key="submit" type="primary" loading={addLoading} onClick={handleAddSubmit}>
            提交
          </Button>,
        ]}
        width={700}
      >
        <Form
          form={addForm}
          layout="vertical"
          name="addDatabaseForm"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="db_name"
                label="数据库名称"
                rules={[{ required: true, message: '请输入数据库名称' }]}
              >
                <Input placeholder="请输入数据库名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="db_ip"
                label="IP地址"
                rules={[{ required: true, message: '请输入IP地址' }]}
              >
                <Input placeholder="请输入IP地址" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="db_type"
                label="数据库类型"
                rules={[{ required: true, message: '请输入数据库类型' }]}
              >
                <Input placeholder="例如：MySQL, Oracle, PostgreSQL" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="purpose"
                label="用途"
                rules={[{ required: true, message: '请输入用途' }]}
              >
                <Input placeholder="请输入数据库用途" />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            name="remark"
            label="备注"
          >
            <Input.TextArea rows={3} placeholder="请输入备注信息（可选）" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 数据库详情弹窗 */}
      <Modal
        title="数据库详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" type="primary" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>,
        ]}
        width={700}
      >
        {currentDatabase ? (
          <div>
            <Descriptions bordered column={2}>
              <Descriptions.Item label="数据库名称" span={2}>{currentDatabase.db_name || '-'}</Descriptions.Item>
              <Descriptions.Item label="IP地址">{currentDatabase.db_ip || '-'}</Descriptions.Item>
              <Descriptions.Item label="数据库类型">{currentDatabase.db_type || '-'}</Descriptions.Item>
              <Descriptions.Item label="用途" span={2}>{currentDatabase.purpose || '-'}</Descriptions.Item>
              <Descriptions.Item label="创建时间">{currentDatabase.creat_time || '-'}</Descriptions.Item>
              <Descriptions.Item label="状态">
                {currentDatabase.db_status === 'online' ? (
                  <Tag color="green">在线</Tag>
                ) : (
                  <Tag color="red">已下线</Tag>
                )}
              </Descriptions.Item>
              {currentDatabase.offline_time && (
                <Descriptions.Item label="下线时间" span={2}>{currentDatabase.offline_time}</Descriptions.Item>
              )}
              <Descriptions.Item label="备注" span={2}>{currentDatabase.remark || '-'}</Descriptions.Item>
            </Descriptions>
          </div>
        ) : (
          <Empty description="未找到数据库信息" />
        )}
      </Modal>
    </Card>
  );
}

export default DbQuery;
