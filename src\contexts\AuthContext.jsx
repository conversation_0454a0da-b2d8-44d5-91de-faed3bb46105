import React, { createContext, useContext, useState, useEffect } from 'react';

const AuthContext = createContext();

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true); // 添加加载状态

  // 从本地存储加载用户信息
  useEffect(() => {
    const loadUser = async () => {
      const storedUser = localStorage.getItem('user');
      if (storedUser) {
        try {
          const parsedUser = JSON.parse(storedUser);
          setUser(parsedUser);

          // 等待令牌验证完成
          await validateToken(parsedUser);
        } catch (error) {
          console.error('Failed to parse stored user data:', error);
          localStorage.removeItem('user');
          setUser(null);
        }
      }
      setLoading(false); // 完成初始加载
    };

    loadUser();

    // 设置定期刷新 token 的定时器（每20分钟刷新一次）
    const tokenRefreshInterval = setInterval(async () => {
      const currentUser = localStorage.getItem('user');
      if (currentUser) {
        try {
          const parsedUser = JSON.parse(currentUser);
          await validateToken(parsedUser);
        } catch (error) {
          console.error('Token refresh error:', error);
        }
      }
    }, 20 * 60 * 1000); // 20分钟

    // 清理定时器
    return () => {
      clearInterval(tokenRefreshInterval);
    };
  }, []);

  // 验证令牌有效性
  const validateToken = async (userData) => {
    try {
      const response = await fetch('/api/validate-token', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${userData.token}`,
          'Content-Type': 'application/json',
        },
        // 添加 credentials 选项，确保发送 cookies
        credentials: 'include'
      });

      if (!response.ok) {
        // 令牌无效，清除用户状态
        setUser(null);
        localStorage.removeItem('user');
        return false;
      }

      // 检查是否有新的 token 返回
      const data = await response.json();
      if (data.new_token) {
        // 更新本地存储中的 token
        userData.token = data.new_token;
        setUser({...userData});
        localStorage.setItem('user', JSON.stringify(userData));
      }

      return true;
    } catch (error) {
      console.error('Token validation error:', error);
      // 出错时保持用户登录状态，避免因网络问题导致退出
      return true; // 网络错误时仍然保持登录状态
    }
  };

  // 登录函数
  const login = async (username, password, userType = 'local') => {
    try {
      const response = await fetch('/api/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password, user_type: userType }),
        // 添加 credentials 选项，确保接收 cookies
        credentials: 'include'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || '登录失败');
      }

      const userData = await response.json();

      // 保存用户信息到状态和本地存储
      setUser(userData);
      localStorage.setItem('user', JSON.stringify(userData));

      return userData;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  };

  // 登出函数
  const logout = () => {
    setUser(null);
    localStorage.removeItem('user');
  };

  const value = {
    user,
    loading,
    login,
    logout,
    isAuthenticated: () => !!user,
    hasMenuPermission: (menuName) => {
      if (!user || !user.permissions || !user.permissions.modules) {
        return false;
      }

      // 如果用户是本地管理员，则拥有所有权限
      if (user.is_local_admin) {
        return true;
      }

      // 检查用户是否有该菜单的权限
      for (const module of user.permissions.modules) {
        if (!module.menus) continue;

        for (const menu of module.menus) {
          if (menu.menu_name === menuName) {
            return true;
          }
        }
      }

      return false;
    },
    isLocalAdmin: () => user && user.is_local_admin === true
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  return useContext(AuthContext);
   }
