apiVersion: apps/v1
kind: Deployment
metadata:
  name: icms2-web
  labels:
    app: icms2-web
spec:
  replicas: 2
  selector:
    matchLabels:
      app: icms2-web
  template:
    metadata:
      labels:
        app: icms2-web
    spec:
      containers:
      - name: icms2-web
        image: ${DOCKER_REGISTRY}/icms2-web:${IMAGE_TAG}
        imagePullPolicy: Always
        ports:
        - containerPort: 80
        resources:
          requests:
            cpu: "100m"
            memory: "128Mi"
          limits:
            cpu: "500m"
            memory: "512Mi"
        livenessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
        readinessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 10
        env:
        - name: API_URL
          valueFrom:
            configMapKeyRef:
              name: icms2-web-config
              key: api_url
      imagePullSecrets:
      - name: regcred
