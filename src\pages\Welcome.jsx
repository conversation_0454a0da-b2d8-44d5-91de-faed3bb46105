import React from 'react';
import { Typography, Card, Space, Row, Col } from 'antd';
import { useAuth } from '../contexts/AuthContext';
import {
  AppstoreOutlined,
  DesktopOutlined,
  DatabaseOutlined,
  UserOutlined,
  FileTextOutlined,
  PhoneOutlined,
} from '@ant-design/icons';

const { Title, Text } = Typography;

function Welcome() {
  const { user } = useAuth();
  
  const iconStyle = {
    fontSize: '48px',
    color: '#1890ff',
    margin: '0 auto 16px',
    display: 'block',
  };
  
  const cardStyle = {
    textAlign: 'center',
    height: '100%',
    transition: 'all 0.3s',
    cursor: 'pointer',
  };
  
  const hoverableCardStyle = {
    ...cardStyle,
    '&:hover': {
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
      transform: 'translateY(-5px)',
    },
  };

  return (
    <div style={{ padding: '24px', width: '100%' }}>
      <div style={{ 
        textAlign: 'center', 
        marginBottom: '48px',
        background: 'linear-gradient(135deg, #1890ff 0%, #52c41a 100%)',
        padding: '48px 24px',
        borderRadius: '8px',
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
      }}>
        <Title level={1} style={{ 
          color: 'white', 
          fontSize: '42px',
          marginBottom: '16px',
          textShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',
        }}>
          欢迎使用和泰基础资源管理系统
        </Title>
        
        <Text style={{ 
          color: 'rgba(255, 255, 255, 0.85)', 
          fontSize: '18px',
          display: 'block',
        }}>
          您好，{user?.display_name || '用户'}！今天是 {new Date().toLocaleDateString('zh-CN', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}
        </Text>
      </div>
      
      <Title level={3} style={{ 
        marginBottom: '24px', 
        textAlign: 'center',
        fontSize: '28px',  // 放大字体
        fontWeight: 'bold' // 加粗显示
      }}>
        系统功能
      </Title>
      
      <div style={{ 
        textAlign: 'center', 
        marginBottom: '32px',
        color: '#666'
      }}>
        (这里纯粹为了让页面显得饱满而展示，点不了哈，使用具体功能请点侧边栏！！)
      </div>
      
      <Row gutter={[24, 24]}>
        <Col xs={24} sm={12} md={8}>
          <Card hoverable style={hoverableCardStyle}>
            <AppstoreOutlined style={iconStyle} />
            <Title level={4}>应用查询</Title>
            <Text type="secondary">查询和管理系统中的应用信息</Text>
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={8}>
          <Card hoverable style={hoverableCardStyle}>
            <DesktopOutlined style={iconStyle} />
            <Title level={4}>主机查询</Title>
            <Text type="secondary">查询和管理服务器及主机信息</Text>
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={8}>
          <Card hoverable style={hoverableCardStyle}>
            <DatabaseOutlined style={iconStyle} />
            <Title level={4}>数据库查询</Title>
            <Text type="secondary">查询和管理数据库资源信息</Text>
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={8}>
          <Card hoverable style={hoverableCardStyle}>
            <FileTextOutlined style={iconStyle} />
            <Title level={4}>文件存储查询</Title>
            <Text type="secondary">查询和管理文件存储资源</Text>
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={8}>
          <Card hoverable style={hoverableCardStyle}>
            <PhoneOutlined style={iconStyle} />
            <Title level={4}>分机号查询</Title>
            <Text type="secondary">查询和管理电话分机号信息</Text>
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={8}>
          <Card hoverable style={hoverableCardStyle}>
            <UserOutlined style={iconStyle} />
            <Title level={4}>用户管理</Title>
            <Text type="secondary">管理系统用户和权限设置</Text>
          </Card>
        </Col>
      </Row>
    </div>
  );
}

export default Welcome;

