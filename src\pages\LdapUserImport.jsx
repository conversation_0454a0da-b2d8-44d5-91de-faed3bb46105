import { useState, useEffect } from 'react';
import { Card, Table, Button, Select, Input, message, Modal, Form, Space, Tag, Spin } from 'antd';
import { UserAddOutlined, SearchOutlined, SyncOutlined } from '@ant-design/icons';
import { useAuth } from '../contexts/AuthContext';

const { Option } = Select;

function LdapUserImport() {
  const { isLocalAdmin } = useAuth();
  const [loading, setLoading] = useState(false);
  const [ldapConfigs, setLdapConfigs] = useState([]);
  const [selectedConfig, setSelectedConfig] = useState(null);
  const [searchUsername, setSearchUsername] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [searchLoading, setSearchLoading] = useState(false);
  const [importLoading, setImportLoading] = useState(false);
  const [searchModalVisible, setSearchModalVisible] = useState(false);
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [importResult, setImportResult] = useState(null);

  // 获取LDAP配置列表
  const fetchLdapConfigs = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/ldap-configs');
      if (!response.ok) throw new Error('获取LDAP配置失败');
      const data = await response.json();
      setLdapConfigs(data.filter(config => config.is_active));
      if (data.length > 0 && data.some(config => config.is_active)) {
        setSelectedConfig(data.find(config => config.is_active).id);
      }
    } catch (error) {
      console.error('获取LDAP配置错误:', error);
      message.error('获取LDAP配置失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLdapConfigs();
  }, []);

  // 搜索LDAP用户
  const handleSearch = async () => {
    if (!selectedConfig) {
      message.warning('请先选择LDAP配置');
      return;
    }
    
    if (!searchUsername) {
      message.warning('请输入要搜索的用户名');
      return;
    }
    
    setSearchLoading(true);
    try {
      const response = await fetch('/api/ldap-test-search', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          config_id: selectedConfig,
          username: searchUsername
        }),
      });
      
      if (!response.ok) throw new Error('LDAP用户搜索失败');
      
      const data = await response.json();
      if (data.found) {
        setSearchResults(data.users);
        setSearchModalVisible(true);
      } else {
        message.info(data.message || '未找到匹配的用户');
        setSearchResults([]);
      }
    } catch (error) {
      console.error('LDAP用户搜索错误:', error);
      message.error('LDAP用户搜索失败: ' + error.message);
    } finally {
      setSearchLoading(false);
    }
  };

  // 导入LDAP用户
  const handleImport = async () => {
    if (!selectedConfig) {
      message.warning('请先选择LDAP配置');
      return;
    }
    
    setImportLoading(true);
    try {
      const response = await fetch('/api/ldap-fetch-users', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          config_id: selectedConfig
        }),
      });
      
      if (!response.ok) throw new Error('LDAP用户导入失败');
      
      const data = await response.json();
      setImportResult(data);
      setImportModalVisible(true);
      message.success(data.message || '用户导入成功');
    } catch (error) {
      console.error('LDAP用户导入错误:', error);
      message.error('LDAP用户导入失败: ' + error.message);
    } finally {
      setImportLoading(false);
    }
  };

  // 搜索结果列定义
  const searchColumns = [
    { 
      title: '显示名称', 
      dataIndex: 'displayName', 
      key: 'displayName',
      render: (text) => text || '未设置'
    },
    { 
      title: '用户名', 
      dataIndex: 'sAMAccountName', 
      key: 'sAMAccountName',
      render: (text) => text || '未设置'
    },
    { 
      title: '邮箱', 
      dataIndex: 'mail', 
      key: 'mail',
      render: (text) => text || '未设置'
    },
    { 
      title: '通用名称', 
      dataIndex: 'cn', 
      key: 'cn',
      render: (text) => text || '未设置'
    }
  ];

  return (
    <Card title="LDAP用户导入" style={{ maxWidth: 1200, margin: '0 auto' }}>
      <Form layout="inline" style={{ marginBottom: 16 }}>
        <Form.Item label="LDAP配置">
          <Select 
            value={selectedConfig} 
            onChange={setSelectedConfig} 
            style={{ width: 250 }}
            loading={loading}
            placeholder="选择LDAP配置"
          >
            {ldapConfigs.map(config => (
              <Option key={config.id} value={config.id}>
                {config.server}:{config.port} ({config.bind_dn})
              </Option>
            ))}
          </Select>
        </Form.Item>
        
        <Form.Item label="用户名">
          <Input 
            placeholder="输入要搜索的用户名" 
            value={searchUsername} 
            onChange={(e) => setSearchUsername(e.target.value)}
            style={{ width: 200 }}
            onPressEnter={handleSearch}
          />
        </Form.Item>
        
        <Form.Item>
          <Space>
            <Button 
              type="primary" 
              icon={<SearchOutlined />} 
              onClick={handleSearch}
              loading={searchLoading}
              disabled={!selectedConfig || !searchUsername}
            >
              搜索用户
            </Button>
            
            <Button 
              type="primary" 
              icon={<UserAddOutlined />} 
              onClick={handleImport}
              loading={importLoading}
              disabled={!selectedConfig || !isLocalAdmin()}
            >
              导入所有用户
            </Button>
          </Space>
        </Form.Item>
      </Form>
      
      {/* 搜索结果模态框 */}
      <Modal
        title="LDAP用户搜索结果"
        open={searchModalVisible}
        onCancel={() => setSearchModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setSearchModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        <Table 
          columns={searchColumns} 
          dataSource={searchResults.map((user, index) => ({ ...user, key: index }))} 
          bordered
          pagination={false}
          size="small"
        />
      </Modal>
      
      {/* 导入结果模态框 */}
      <Modal
        title="LDAP用户导入结果"
        open={importModalVisible}
        onCancel={() => setImportModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setImportModalVisible(false)}>
            关闭
          </Button>
        ]}
      >
        {importResult && (
          <div>
            <p>导入结果: {importResult.message}</p>
            <p>新增用户: <Tag color="green">{importResult.added}</Tag></p>
            <p>更新用户: <Tag color="blue">{importResult.updated}</Tag></p>
            <p>总计处理: <Tag color="purple">{importResult.total}</Tag></p>
            <p style={{ marginTop: 16 }}>
              注意: 导入的用户需要单独分配角色才能获得权限。
            </p>
          </div>
        )}
      </Modal>
      
      <div style={{ marginTop: 24 }}>
        <h3>LDAP用户导入说明</h3>
        <ul>
          <li>系统会连接到选定的LDAP服务器，搜索并导入用户信息</li>
          <li>导入的用户类型为 'ldap'，需要单独分配角色才能获得权限</li>
          <li>如果用户已存在，系统会更新用户信息</li>
          <li>您可以先使用"搜索用户"功能测试LDAP连接是否正常</li>
          <li>只有本地管理员可以执行导入操作</li>
        </ul>
      </div>
    </Card>
  );
}

export default LdapUserImport;