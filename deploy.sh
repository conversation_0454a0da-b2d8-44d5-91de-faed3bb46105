#!/bin/bash
set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 打印带颜色的信息
info() {
  echo -e "${GREEN}[INFO] $1${NC}"
}

warn() {
  echo -e "${YELLOW}[WARN] $1${NC}"
}

error() {
  echo -e "${RED}[ERROR] $1${NC}"
  exit 1
}

# 检查必要的命令是否存在
check_commands() {
  info "检查必要的命令..."
  
  if ! command -v docker &> /dev/null; then
    error "未找到 docker 命令，请先安装 Docker"
  fi
  
  if ! command -v kubectl &> /dev/null; then
    error "未找到 kubectl 命令，请先安装 kubectl"
  fi
  
  if ! command -v envsubst &> /dev/null; then
    error "未找到 envsubst 命令，请先安装 gettext 包"
  fi
}

# 检查必要的环境变量
check_env_vars() {
  info "检查环境变量..."
  
  if [ -z "$DOCKER_REGISTRY" ]; then
    warn "未设置 DOCKER_REGISTRY 环境变量，使用默认值 'localhost:5000'"
    export DOCKER_REGISTRY="localhost:5000"
  fi
  
  if [ -z "$IMAGE_TAG" ]; then
    warn "未设置 IMAGE_TAG 环境变量，使用默认值 'latest'"
    export IMAGE_TAG="latest"
  fi
  
  if [ -z "$NAMESPACE" ]; then
    warn "未设置 NAMESPACE 环境变量，使用默认值 'default'"
    export NAMESPACE="default"
  fi
  
  if [ -z "$INGRESS_HOST" ]; then
    warn "未设置 INGRESS_HOST 环境变量，使用默认值 'icms2.example.com'"
    export INGRESS_HOST="icms2.example.com"
  fi
  
  if [ -z "$API_URL" ]; then
    warn "未设置 API_URL 环境变量，使用默认值 'http://icms2-api-service'"
    export API_URL="http://icms2-api-service"
  fi
  
  info "使用以下配置:"
  echo "  DOCKER_REGISTRY: $DOCKER_REGISTRY"
  echo "  IMAGE_TAG: $IMAGE_TAG"
  echo "  NAMESPACE: $NAMESPACE"
  echo "  INGRESS_HOST: $INGRESS_HOST"
  echo "  API_URL: $API_URL"
}

# 构建并推送Docker镜像
build_and_push() {
  info "构建Docker镜像..."
  docker build -t ${DOCKER_REGISTRY}/icms2-web:${IMAGE_TAG} .
  
  info "推送Docker镜像到仓库..."
  docker push ${DOCKER_REGISTRY}/icms2-web:${IMAGE_TAG}
}

# 处理Kubernetes配置文件
process_k8s_files() {
  info "处理Kubernetes配置文件..."
  
  # 创建临时目录
  mkdir -p .tmp_k8s
  
  # 复制并处理配置文件
  for file in k8s/*.yaml; do
    basename=$(basename $file)
    envsubst < $file > .tmp_k8s/$basename
  done
}

# 部署到Kubernetes
deploy_to_k8s() {
  info "确保命名空间存在..."
  kubectl create namespace ${NAMESPACE} --dry-run=client -o yaml | kubectl apply -f -
  
  info "应用Kubernetes配置..."
  kubectl apply -k .tmp_k8s/
  
  # 清理临时文件
  rm -rf .tmp_k8s
}

# 验证部署
verify_deployment() {
  info "等待部署完成..."
  kubectl rollout status deployment/icms2-web -n ${NAMESPACE} --timeout=300s
  
  info "检查Pod状态..."
  kubectl get pods -n ${NAMESPACE} -l app=icms2-web
  
  info "检查Service..."
  kubectl get svc -n ${NAMESPACE} -l app=icms2-web
  
  info "检查Ingress..."
  kubectl get ingress -n ${NAMESPACE} -l app=icms2-web
  
  info "部署完成！"
  echo "您可以通过以下地址访问应用："
  echo "http://${INGRESS_HOST}"
}

# 主函数
main() {
  info "开始部署 ICMS2-Web 应用..."
  
  check_commands
  check_env_vars
  
  read -p "是否构建并推送Docker镜像？(y/n) " -n 1 -r
  echo
  if [[ $REPLY =~ ^[Yy]$ ]]; then
    build_and_push
  fi
  
  process_k8s_files
  deploy_to_k8s
  verify_deployment
}

# 执行主函数
main
