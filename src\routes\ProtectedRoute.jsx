import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

// 根据路由路径获取对应的菜单名称
const getMenuNameFromPath = (path) => {
  // 路径到菜单名称的映射
  const pathToMenuMap = {
    '/': 'home',
    '/app-query': 'app_query',
    '/host-query': 'host_query',
    '/db-query': 'db_query',
    '/load-balancer-query': 'lb_query', // 修改为数据库中的menu_name
    '/file-storage-query': 'file_storage_query',
    '/line-query': 'line_query',
    '/extension-query': 'extension_query',
    '/log-download': 'log_download',
    '/user-perm-query': 'user_perm_query',
    '/system-management': 'system_management',
    '/extension-management': 'extension_management',
    '/line-management': 'line_management',
    '/user-query': 'user_query',
    '/ldap-config': 'ldap_config',
    '/ldap-user-import': 'ldap_user_import',
    '/role-manage': 'role_manage',
    '/perm-assign': 'perm_assign',
  };

  return pathToMenuMap[path] || null;
};

function ProtectedRoute({ children, path }) {
  const { user, isAuthenticated, hasMenuPermission } = useAuth();
  const location = useLocation();

  // 如果用户未登录，重定向到登录页面
  if (!isAuthenticated()) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // 首页所有人都可以访问
  if (path === '/') {
    return children;
  }

  // 获取当前路径对应的菜单名称
  const menuName = getMenuNameFromPath(path);

  // 如果用户没有该菜单的权限，重定向到首页
  // 但是如果用户是本地管理员，则允许访问所有页面
  if (menuName && !hasMenuPermission(menuName) && !user.is_local_admin) {
    console.warn(`User does not have permission to access ${path}`);
    return <Navigate to="/" replace />;
  }

  return children;
}

export default ProtectedRoute;





