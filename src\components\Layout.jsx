import React, { useState, useEffect } from 'react';
import { Layout, Menu, Button, Avatar, Dropdown, Space } from 'antd';
import {
  MenuUnfoldOutlined,
  MenuFoldOutlined,
  UserOutlined,
  HomeOutlined,
  SearchOutlined,
  AppstoreOutlined,
  DesktopOutlined,
  DatabaseOutlined,
  FileTextOutlined,
  LinkOutlined,
  PhoneOutlined,
  DownloadOutlined,
  LockOutlined,
  SettingOutlined,
  NumberOutlined,
  TeamOutlined,
  SafetyCertificateOutlined,
  UserAddOutlined,
  LogoutOutlined,
  CloudOutlined
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const { Header, Sider, Content } = Layout;

function AppLayout({ children }) {
  const [collapsed, setCollapsed] = useState(false);
  const { user, logout, hasMenuPermission } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // 定义菜单项和对应的图标
  const menuIconMap = {
    'home': <HomeOutlined />,
    'resource_query': <SearchOutlined />,
    'app_query': <AppstoreOutlined />,
    'host_query': <DesktopOutlined />,
    'db_query': <DatabaseOutlined />,
    'lb_query': <CloudOutlined />, // 修改为数据库中的menu_name
    'file_storage_query': <FileTextOutlined />,
    'line_query': <LinkOutlined />,
    'extension_query': <PhoneOutlined />,
    'log_download': <DownloadOutlined />,
    'user_perm_query': <LockOutlined />,
    'basic_data': <DatabaseOutlined />,
    'system_management': <SettingOutlined />,
    'extension_management': <NumberOutlined />,
    'line_management': <LinkOutlined />,
    'user_management': <UserOutlined />,
    'user_query': <UserOutlined />,
    'ldap_config': <LockOutlined />,
    'ldap_user_import': <UserAddOutlined />,
    'role_manage': <TeamOutlined />,
    'perm_assign': <SafetyCertificateOutlined />,
  };

  // 定义菜单项和对应的路由路径
  const menuRouteMap = {
    'home': '/',
    'app_query': '/app-query',
    'host_query': '/host-query',
    'db_query': '/db-query',
    'lb_query': '/load-balancer-query', // 修改为数据库中的menu_name
    'file_storage_query': '/file-storage-query',
    'line_query': '/line-query',
    'extension_query': '/extension-query',
    'log_download': '/log-download',
    'user_perm_query': '/user-perm-query',
    'system_management': '/system-management',
    'extension_management': '/extension-management',
    'line_management': '/line-management',
    'user_query': '/user-query',
    'ldap_config': '/ldap-config',
    'ldap_user_import': '/ldap-user-import',
    'role_manage': '/role-manage',
    'perm_assign': '/perm-assign',
  };

  // 获取当前选中的菜单项
  const getSelectedKeys = () => {
    const path = location.pathname;
    for (const [key, route] of Object.entries(menuRouteMap)) {
      if (route === path) {
        return [key];
      }
    }
    return ['home'];
  };

  // 处理菜单点击事件
  const handleMenuClick = (e) => {
    const route = menuRouteMap[e.key];
    if (route) {
      navigate(route);
    }
  };

  // 构建菜单项
  const buildMenuItems = () => {
    if (!user || !user.permissions || !user.permissions.modules) {
      return [
        {
          key: 'home',
          icon: menuIconMap['home'],
          label: '首页',
        }
      ];
    }

    // 首页菜单项（所有用户都可见）
    const menuItems = [
      {
        key: 'home',
        icon: menuIconMap['home'],
        label: '首页',
      }
    ];

    // 创建一个跟踪已添加菜单的集合
    const addedMenuKeys = new Set(['home']);

    // 如果用户是本地管理员，先添加管理菜单
    if (user.is_local_admin) {
      // 添加用户管理模块
      const userManagementModule = {
        key: 'user_management',
        icon: menuIconMap['user_management'] || <UserOutlined />,
        label: '用户管理',
        children: [
          {
            key: 'user_query',
            icon: menuIconMap['user_query'] || <UserOutlined />,
            label: '用户查询',
          },
          {
            key: 'ldap_config',
            icon: menuIconMap['ldap_config'] || <LockOutlined />,
            label: 'LDAP配置管理',
          },
          {
            key: 'ldap_user_import',
            icon: menuIconMap['ldap_user_import'] || <UserAddOutlined />,
            label: 'LDAP用户导入',
          },
          {
            key: 'role_manage',
            icon: menuIconMap['role_manage'] || <TeamOutlined />,
            label: '角色管理',
          },
          {
            key: 'perm_assign',
            icon: menuIconMap['perm_assign'] || <SafetyCertificateOutlined />,
            label: '用户权限分配',
          }
        ]
      };

      menuItems.push(userManagementModule);

      // 将所有添加的菜单键添加到集合中
      addedMenuKeys.add('user_management');
      addedMenuKeys.add('user_query');
      addedMenuKeys.add('ldap_config');
      addedMenuKeys.add('ldap_user_import');
      addedMenuKeys.add('role_manage');
      addedMenuKeys.add('perm_assign');
    }

    // 根据用户权限构建其他菜单
    user.permissions.modules.forEach(module => {
      // 如果模块没有菜单或已添加，则跳过
      if (!module.menus || module.menus.length === 0 || addedMenuKeys.has(module.module_name)) return;

      // 创建模块子菜单项
      const subMenuItems = [];

      module.menus.forEach(menu => {
        // 如果菜单已添加，则跳过
        if (addedMenuKeys.has(menu.menu_name)) return;

        subMenuItems.push({
          key: menu.menu_name,
          icon: menuIconMap[menu.menu_name] || <AppstoreOutlined />,
          label: menu.name,
        });

        addedMenuKeys.add(menu.menu_name);
      });

      // 只有当有子菜单时才添加模块菜单项
      if (subMenuItems.length > 0) {
        menuItems.push({
          key: module.module_name,
          icon: menuIconMap[module.module_name] || <AppstoreOutlined />,
          label: module.name,
          children: subMenuItems,
        });

        addedMenuKeys.add(module.module_name);
      }
    });

    return menuItems;
  };

  // 用户菜单项
  const userMenuItems = [
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: () => {
        logout();
        navigate('/login');
      },
    },
  ];

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider trigger={null} collapsible collapsed={collapsed}>
        <div className="logo" style={{
          height: '32px',
          margin: '16px',
          background: 'rgba(255, 255, 255, 0.3)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          color: 'white',
          fontWeight: 'bold'
        }}>
          {collapsed ? 'ICMS' : '和泰基础资源管理系统'}
        </div>
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={getSelectedKeys()}
          items={buildMenuItems()}
          onClick={handleMenuClick}
          style={{ textAlign: 'left' }}
        />
      </Sider>
      <Layout className="site-layout">
        <Header className="site-layout-background" style={{
          padding: 0,
          background: '#fff',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          {React.createElement(collapsed ? MenuUnfoldOutlined : MenuFoldOutlined, {
            className: 'trigger',
            onClick: () => setCollapsed(!collapsed),
            style: { padding: '0 24px', fontSize: '18px' }
          })}

          <div style={{ marginRight: '24px' }}>
            <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
              <Space style={{ cursor: 'pointer' }}>
                <Avatar icon={<UserOutlined />} />
                <span>{user?.display_name || user?.username || '用户'}</span>
              </Space>
            </Dropdown>
          </div>
        </Header>
        <Content
          className="site-layout-background"
          style={{
            margin: '24px 16px',
            padding: 24,
            minHeight: 280,
            overflow: 'auto',
            width: 'auto'
          }}
        >
          {children}
        </Content>
      </Layout>
    </Layout>
  );
}

export default AppLayout;
