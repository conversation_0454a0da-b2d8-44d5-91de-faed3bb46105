import React, { useState, useEffect } from 'react';
import { Card, Table, Button, Select, Space, Modal, Transfer, Tabs, Tag, message, Checkbox, Row, Col } from 'antd';
import { useAuth } from '../contexts/AuthContext';

const { Option } = Select;
const { TabPane } = Tabs;

function PermAssign() {
  const { user, isLocalAdmin } = useAuth();
  const [loading, setLoading] = useState(false);
  const [users, setUsers] = useState([]);
  const [roles, setRoles] = useState([]);
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [selectedUserType, setSelectedUserType] = useState('ldap');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedRoles, setSelectedRoles] = useState([]);
  const [userRolesModalVisible, setUserRolesModalVisible] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const [userRoles, setUserRoles] = useState({
    assigned_roles: [],
    all_roles: []
  });
  const [userRolesLoading, setUserRolesLoading] = useState(false);
  const [batchModalVisible, setBatchModalVisible] = useState(false);
  const [batchSelectedUsers, setBatchSelectedUsers] = useState([]);
  const [batchSelectedRoles, setBatchSelectedRoles] = useState([]);

  // 获取用户列表
  const fetchUsers = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/users');
      if (!response.ok) throw new Error('获取用户列表失败');
      const data = await response.json();
      setUsers(data.map((user, index) => ({ ...user, key: user.id, index: index + 1 })));
    } catch (error) {
      console.error('获取用户列表错误:', error);
      message.error('获取用户列表失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // 获取所有角色
  const fetchRoles = async () => {
    try {
      const response = await fetch('/api/roles');
      if (!response.ok) throw new Error('获取角色列表失败');
      const data = await response.json();
      setRoles(data.map(role => ({ ...role, key: role.id })));
    } catch (error) {
      console.error('获取角色列表错误:', error);
      message.error('获取角色列表失败: ' + error.message);
    }
  };

  // 获取用户的角色
  const fetchUserRoles = async (userId, userType = 'ldap') => {
    setUserRolesLoading(true);
    try {
      const response = await fetch(`/api/users/${userId}/roles?user_type=${userType}`);
      if (!response.ok) throw new Error('获取用户角色失败');
      const data = await response.json();
      setUserRoles(data);
    } catch (error) {
      console.error('获取用户角色错误:', error);
      message.error('获取用户角色失败: ' + error.message);
    } finally {
      setUserRolesLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
    fetchRoles();
  }, []);

  // 处理用户选择变化
  const handleUserSelectionChange = (selectedRowKeys) => {
    setSelectedUsers(selectedRowKeys);
  };

  // 打开分配角色模态框
  const handleAssignRoles = (record) => {
    setCurrentUser(record);
    setSelectedUserType(record.user_type || 'ldap');
    fetchUserRoles(record.id, record.user_type || 'ldap');
    setUserRolesModalVisible(true);
  };

  // 保存用户角色分配
  const handleSaveUserRoles = async () => {
    try {
      const response = await fetch(`/api/users/${currentUser.id}/roles`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          roles: selectedRoles,
          user_type: selectedUserType
        }),
      });
      
      if (!response.ok) throw new Error('保存用户角色失败');
      
      message.success('用户角色分配成功');
      setUserRolesModalVisible(false);
      fetchUsers(); // 刷新用户列表
    } catch (error) {
      console.error('保存用户角色错误:', error);
      message.error('保存用户角色失败: ' + error.message);
    }
  };

  // 打开批量分配角色模态框
  const handleBatchAssign = () => {
    if (selectedUsers.length === 0) {
      message.warning('请先选择用户');
      return;
    }
    setBatchSelectedUsers(selectedUsers);
    setBatchSelectedRoles([]);
    setBatchModalVisible(true);
  };

  // 保存批量角色分配
  const handleSaveBatchRoles = async () => {
    if (batchSelectedRoles.length === 0) {
      message.warning('请选择至少一个角色');
      return;
    }

    try {
      const response = await fetch('/api/users/batch-roles', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          users: batchSelectedUsers,
          roles: batchSelectedRoles,
          user_type: selectedUserType
        }),
      });
      
      if (!response.ok) throw new Error('批量分配角色失败');
      
      const result = await response.json();
      message.success(result.message || '批量分配角色成功');
      setBatchModalVisible(false);
      fetchUsers(); // 刷新用户列表
    } catch (error) {
      console.error('批量分配角色错误:', error);
      message.error('批量分配角色失败: ' + error.message);
    }
  };

  // 用户列表列定义
  const columns = [
    { title: '序号', dataIndex: 'index', key: 'index', width: 80 },
    { title: '用户名', dataIndex: 'username', key: 'username' },
    { title: '显示名称', dataIndex: 'display_name', key: 'display_name' },
    { 
      title: '用户类型', 
      dataIndex: 'user_type', 
      key: 'user_type',
      render: (type) => (
        <Tag color={type === 'local' ? 'green' : 'blue'}>
          {type === 'local' ? '本地用户' : 'LDAP用户'}
        </Tag>
      )
    },
    { 
      title: '角色', 
      dataIndex: 'roles', 
      key: 'roles',
      render: (roles) => {
        if (!roles || roles.length === 0) return '无角色';
        return roles.map(role => (
          <Tag color="purple" key={role}>
            {role}
          </Tag>
        ));
      }
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Button 
          type="primary" 
          size="small" 
          onClick={() => handleAssignRoles(record)}
          disabled={!isLocalAdmin()}
        >
          分配角色
        </Button>
      ),
    },
  ];

  // 用户选择配置
  const rowSelection = {
    selectedRowKeys: selectedUsers,
    onChange: handleUserSelectionChange,
  };

  // 当用户角色模态框打开时，设置已选择的角色
  useEffect(() => {
    if (userRolesModalVisible && userRoles.assigned_roles) {
      setSelectedRoles(userRoles.assigned_roles);
    }
  }, [userRolesModalVisible, userRoles]);

  return (
    <Card title="用户权限分配" style={{ maxWidth: 1200, margin: '0 auto' }}>
      <Space style={{ marginBottom: 16 }}>
        <Select 
          value={selectedUserType} 
          onChange={setSelectedUserType} 
          style={{ width: 150 }}
        >
          <Option value="ldap">LDAP用户</Option>
          <Option value="local">本地用户</Option>
        </Select>
        <Button 
          type="primary" 
          onClick={handleBatchAssign} 
          disabled={selectedUsers.length === 0 || !isLocalAdmin()}
        >
          批量分配角色
        </Button>
      </Space>
      
      <Table 
        rowSelection={rowSelection}
        columns={columns} 
        dataSource={users} 
        loading={loading}
        bordered
        pagination={{ pageSize: 10 }}
      />
      
      {/* 用户角色分配模态框 */}
      <Modal
        title={`为用户 "${currentUser?.display_name || currentUser?.username || ''}" 分配角色`}
        open={userRolesModalVisible}
        onCancel={() => setUserRolesModalVisible(false)}
        onOk={handleSaveUserRoles}
        width={700}
        confirmLoading={userRolesLoading}
      >
        <Tabs defaultActiveKey="1">
          <TabPane tab="角色分配" key="1">
            <div style={{ marginBottom: 16 }}>
              <p>用户类型: <Tag color={selectedUserType === 'local' ? 'green' : 'blue'}>
                {selectedUserType === 'local' ? '本地用户' : 'LDAP用户'}
              </Tag></p>
            </div>
            
            {userRolesLoading ? (
              <div>加载中...</div>
            ) : (
              <Transfer
                dataSource={userRoles.all_roles?.map(role => ({
                  key: role.id,
                  title: role.role_name,
                  description: role.description
                })) || []}
                titles={['可用角色', '已分配角色']}
                targetKeys={selectedRoles}
                onChange={setSelectedRoles}
                render={item => item.title}
                listStyle={{ width: 300, height: 300 }}
              />
            )}
          </TabPane>
          <TabPane tab="权限预览" key="2">
            <div style={{ maxHeight: 400, overflow: 'auto' }}>
              {userRoles.all_roles?.filter(role => selectedRoles.includes(role.id)).map(role => (
                <Card key={role.id} title={role.role_name} style={{ marginBottom: 16 }} size="small">
                  <p>{role.description}</p>
                </Card>
              ))}
            </div>
          </TabPane>
        </Tabs>
      </Modal>
      
      {/* 批量分配角色模态框 */}
      <Modal
        title="批量分配角色"
        open={batchModalVisible}
        onCancel={() => setBatchModalVisible(false)}
        onOk={handleSaveBatchRoles}
        width={700}
      >
        <div style={{ marginBottom: 16 }}>
          <p>已选择 <b>{batchSelectedUsers.length}</b> 个用户，用户类型: <Tag color={selectedUserType === 'local' ? 'green' : 'blue'}>
            {selectedUserType === 'local' ? '本地用户' : 'LDAP用户'}
          </Tag></p>
          <p style={{ color: 'red' }}>注意: 此操作将会清除这些用户现有的所有角色，并分配新选择的角色。</p>
        </div>
        
        <div style={{ marginBottom: 16 }}>
          <h4>选择要分配的角色:</h4>
          <Checkbox.Group 
            style={{ width: '100%' }}
            value={batchSelectedRoles}
            onChange={setBatchSelectedRoles}
          >
            <Row>
              {roles.map(role => (
                <Col span={8} key={role.id}>
                  <Checkbox value={role.id}>{role.role_name}</Checkbox>
                </Col>
              ))}
            </Row>
          </Checkbox.Group>
        </div>
      </Modal>
    </Card>
  );
}

export default PermAssign;
