# 版本控制
.git
.gitignore

# 依赖目录
node_modules
dist

# 日志
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 编辑器配置
.vscode
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.DS_Store

# 环境变量
.env
.env.local
.env.development
.env.test
.env.production

# 测试
coverage
__tests__
*.test.js
*.spec.js

# 文档
docs
*.md

# Kubernetes配置
k8s

# Docker相关
Dockerfile
.dockerignore
docker-compose.yml
