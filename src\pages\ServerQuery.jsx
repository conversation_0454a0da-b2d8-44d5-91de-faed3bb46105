import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Button,
  Card,
  Table,
  message,
  Spin,
  Alert,
  Space,
  Tooltip,
  Tag,
  Pagination,
  Row,
  Col,
  Modal,
  Select,
  Popconfirm,
  Descriptions,
  Empty
} from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
  InfoCircleOutlined,
  PlusOutlined,
  CloudSyncOutlined,
  DeleteOutlined,
  EyeOutlined
} from '@ant-design/icons';

function ServerQuery() {
  const [form] = Form.useForm();
  const [addForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [servers, setServers] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
    showSizeChanger: true,
    pageSizeOptions: ['10', '20', '50', '100'],
  });

  // 新增主机相关状态
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [addLoading, setAddLoading] = useState(false);

  // 同步云主机相关状态
  const [syncModalVisible, setSyncModalVisible] = useState(false);
  const [syncLoading, setSyncLoading] = useState(false);
  const [syncResult, setSyncResult] = useState(null);

  // 服务器详情相关状态 - 移除不需要的加载状态
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [currentServer, setCurrentServer] = useState(null);

  // 定义表格列
  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 80,
      render: (_, __, index) => (pagination.current - 1) * pagination.pageSize + index + 1,
    },
    {
      title: '主机名',
      dataIndex: 'hostname',
      key: 'hostname',
      ellipsis: true,
    },
    {
      title: '内网IP',
      dataIndex: 'ipv4',
      key: 'ipv4',
      ellipsis: true,
    },
    {
      title: '公网IP',
      dataIndex: 'public_ip',
      key: 'public_ip',
      ellipsis: true,
      render: (text) => text || '-',
    },
    {
      title: '操作系统',
      dataIndex: 'os',
      key: 'os',
      ellipsis: true,
      render: (text) => text || '-',
    },
    {
      title: 'CPU',
      dataIndex: 'cpu',
      key: 'cpu',
      ellipsis: true,
      render: (text) => text || '-',
    },
    {
      title: '内存',
      dataIndex: 'memory',
      key: 'memory',
      ellipsis: true,
      render: (text) => text || '-',
    },
    {
      title: '地域',
      dataIndex: 'region',
      key: 'region',
      ellipsis: true,
      render: (text) => text || '-',
    },
    {
      title: '来源',
      dataIndex: 'original_source',
      key: 'original_source',
      ellipsis: true,
      render: (text) => {
        let color = 'default';
        if (text === 'tencent') {
          color = 'blue';
          text = '腾讯云';
        } else if (text === 'manual') {
          color = 'green';
          text = '手动录入';
        }
        return <Tag color={color}>{text || '未知'}</Tag>;
      },
    },
    {
      title: '状态',
      dataIndex: 'server_status',
      key: 'server_status',
      width: 100,
      render: (status) => (
        <Tag color={status === 'online' ? 'green' : 'red'}>
          {status === 'online' ? '在线' : '已下线'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space>
          <Button
            type="primary"
            icon={<EyeOutlined />}
            size="small"
            onClick={() => handleViewDetail(record)}
          >
            详情
          </Button>
          <Popconfirm
            title="确定要下线此服务器吗？"
            description="下线后将自动解除与应用环境的绑定关系"
            onConfirm={() => handleOffline(record.id)}
            okText="确定"
            cancelText="取消"
            disabled={record.server_status !== 'online' || record.original_source !== 'manual'}
          >
            <Button
              danger
              icon={<DeleteOutlined />}
              size="small"
              disabled={record.server_status !== 'online' || record.original_source !== 'manual'}
              title={
                record.original_source !== 'manual'
                  ? '只能下线手动录入的服务器'
                  : record.server_status !== 'online'
                    ? '服务器已下线'
                    : '下线服务器'
              }
            >
              下线
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 初始加载
  useEffect(() => {
    fetchServers();
  }, []);

  // 获取服务器列表
  const fetchServers = async (params = {}) => {
    setLoading(true);
    setError(null);

    try {
      // 构建查询参数
      const queryParams = new URLSearchParams({
        page: params.page || pagination.current,
        per_page: params.pageSize || pagination.pageSize,
        hostname: params.hostname || form.getFieldValue('hostname') || '',
        ip: params.ip || form.getFieldValue('ip') || '',
        remark: params.remark || form.getFieldValue('remark') || '',
      });

      const response = await fetch(`/api/servers?${queryParams.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`请求失败，状态码：${response.status}`);
      }

      const data = await response.json();

      // 处理返回数据
      setServers(data.servers || []);
      setPagination({
        ...pagination,
        current: data.page || 1,
        pageSize: data.per_page || 20,
        total: data.total || 0,
      });

      if (data.servers && data.servers.length === 0 && data.total > 0) {
        message.info('没有找到匹配的服务器记录');
      }
    } catch (err) {
      setError(err.message);
      console.error('查询错误:', err);
      message.error(`查询失败：${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 处理表单提交
  const handleSearch = (values) => {
    fetchServers({
      ...values,
      page: 1, // 重置到第一页
    });
  };

  // 处理重置
  const handleReset = () => {
    form.resetFields();
    fetchServers({ page: 1 });
  };

  // 处理分页变化
  const handleTableChange = (pagination, filters, sorter) => {
    fetchServers({
      page: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  // 打开新增主机弹窗
  const showAddModal = () => {
    addForm.resetFields();
    setAddModalVisible(true);
  };

  // 关闭新增主机弹窗
  const handleAddCancel = () => {
    setAddModalVisible(false);
  };

  // 提交新增主机表单
  const handleAddSubmit = async () => {
    try {
      const values = await addForm.validateFields();
      setAddLoading(true);

      try {
        const response = await fetch('/api/servers', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(values),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || '创建服务器失败');
        }

        const data = await response.json();
        message.success('服务器创建成功');
        setAddModalVisible(false);
        fetchServers(); // 刷新列表
      } catch (err) {
        console.error('创建服务器错误:', err);
        message.error(`创建服务器失败: ${err.message}`);
      } finally {
        setAddLoading(false);
      }
    } catch (validationError) {
      console.log('表单验证失败:', validationError);
    }
  };

  // 同步腾讯云主机
  const handleSyncTencentCloud = async () => {
    setSyncLoading(true);
    setSyncResult(null);
    setSyncModalVisible(true);

    try {
      const response = await fetch('/api/servers/sync-tencent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({}), // 发送空的JSON对象，确保Content-Type生效
      });

      // 使用工具函数处理响应
      const { handleApiResponse } = await import('../utils/errorHandler');
      const data = await handleApiResponse(response);

      if (data) {
        setSyncResult(data);
        message.success('腾讯云服务器同步完成');
        fetchServers(); // 刷新列表
      } else {
        throw new Error('同步腾讯云服务器失败：未收到有效响应');
      }
    } catch (err) {
      console.error('同步腾讯云服务器错误:', err);
      message.error(`同步腾讯云服务器失败: ${err.message}`);
      setSyncResult({ error: err.message });
    } finally {
      setSyncLoading(false);
    }
  };

  // 下线服务器函数
  const handleOffline = async (serverId) => {
    try {
      const response = await fetch(`/api/servers/${serverId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({}), // 发送空的JSON对象，确保Content-Type生效
      });

      const data = await response.json();

      if (!response.ok) {
        // 处理不同类型的错误
        if (response.status === 403) {
          message.error(data.error || '只能下线手动录入的服务器');
        } else if (response.status === 400) {
          message.warning(data.message || '服务器已经是下线状态');
        } else if (response.status === 404) {
          message.error('服务器不存在');
        } else {
          throw new Error(data.error || data.message || '下线服务器失败');
        }
        return;
      }

      message.success(data.message || '服务器已成功下线');
      fetchServers(); // 刷新列表
    } catch (err) {
      console.error('下线服务器错误:', err);
      message.error(`下线服务器失败: ${err.message}`);
    }
  };

  // 查看服务器详情 - 优化后不再请求后端
  const handleViewDetail = (server) => {
    setCurrentServer(server);
    setDetailModalVisible(true);
  };

  return (
    <Card
      title="服务器资源查询"
      style={{ minHeight: 400 }}
      extra={
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={showAddModal}
          >
            新增
          </Button>
          <Button
            type="default"
            icon={<CloudSyncOutlined />}
            onClick={handleSyncTencentCloud}
            loading={syncLoading}
          >
            同步云主机
          </Button>
        </Space>
      }
    >
      <Form
        form={form}
        name="serverQueryForm"
        onFinish={handleSearch}
        layout="vertical"
      >
        <Row gutter={16}>
          <Col xs={24} sm={12} md={8}>
            <Form.Item name="hostname" label="主机名">
              <Input placeholder="输入主机名关键字" allowClear />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Form.Item name="ip" label="IP地址">
              <Input placeholder="输入IP地址关键字" allowClear />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Form.Item name="remark" label="备注">
              <Input placeholder="输入备注关键字" allowClear />
            </Form.Item>
          </Col>
        </Row>
        <Row>
          <Col span={24} style={{ textAlign: 'right', marginBottom: 16 }}>
            <Space>
              <Button type="primary" htmlType="submit" icon={<SearchOutlined />} loading={loading}>
                查询
              </Button>
              <Button icon={<ReloadOutlined />} onClick={handleReset}>
                重置
              </Button>
            </Space>
          </Col>
        </Row>
      </Form>

      {error && (
        <Alert
          message="查询失败"
          description={`错误信息：${error}`}
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
          closable
          onClose={() => setError(null)}
        />
      )}

      <Table
        columns={columns}
        dataSource={servers.map((server, index) => ({ ...server, key: server.id || index }))}
        pagination={false}
        loading={loading}
        onChange={handleTableChange}
        scroll={{ x: 'max-content' }}
        size="middle"
      />

      <div style={{ marginTop: 16, textAlign: 'right' }}>
        <Pagination
          current={pagination.current}
          pageSize={pagination.pageSize}
          total={pagination.total}
          showSizeChanger
          showQuickJumper
          showTotal={(total) => `共 ${total} 条记录`}
          onChange={(page, pageSize) => fetchServers({ page, pageSize })}
          onShowSizeChange={(current, size) => fetchServers({ page: 1, pageSize: size })}
        />
      </div>

      {/* 新增主机弹窗 */}
      <Modal
        title="新增服务器"
        open={addModalVisible}
        onCancel={handleAddCancel}
        footer={[
          <Button key="cancel" onClick={handleAddCancel}>
            取消
          </Button>,
          <Button key="submit" type="primary" loading={addLoading} onClick={handleAddSubmit}>
            提交
          </Button>,
        ]}
        width={700}
      >
        <Form
          form={addForm}
          layout="vertical"
          name="addServerForm"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="hostname"
                label="主机名"
                rules={[{ required: true, message: '请输入主机名' }]}
              >
                <Input placeholder="请输入主机名" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="ipv4"
                label="内网IP"
                rules={[{ required: true, message: '请输入内网IP' }]}
              >
                <Input placeholder="请输入内网IP" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="public_ip"
                label="公网IP"
              >
                <Input placeholder="请输入公网IP（可选）" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="os"
                label="操作系统"
                rules={[{ required: true, message: '请输入操作系统' }]}
              >
                <Input placeholder="例如：CentOS 7.9" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="cpu"
                label="CPU"
                rules={[{ required: true, message: '请输入CPU信息' }]}
              >
                <Input placeholder="例如：4核" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="memory"
                label="内存"
                rules={[{ required: true, message: '请输入内存信息' }]}
              >
                <Input placeholder="例如：8GB" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="region"
                label="地域"
                rules={[{ required: true, message: '请输入地域' }]}
              >
                <Input placeholder="例如：ap-guangzhou" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="availability_zone"
                label="可用区"
              >
                <Input placeholder="例如：ap-guangzhou-3（可选）" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="vpc_id"
                label="VPC ID"
              >
                <Input placeholder="请输入VPC ID（可选）" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="subnet_id"
                label="子网ID"
              >
                <Input placeholder="请输入子网ID（可选）" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="original_source"
                label="来源"
                initialValue="manual"
              >
                <Input placeholder="默认为manual" disabled />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="physical_host_id"
                label="物理主机ID"
              >
                <Input placeholder="请输入物理主机ID（可选）" />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            name="remark"
            label="备注"
          >
            <Input.TextArea rows={3} placeholder="请输入备注信息（可选）" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 同步云主机结果弹窗 */}
      <Modal
        title="同步腾讯云服务器结果"
        open={syncModalVisible}
        onCancel={() => setSyncModalVisible(false)}
        footer={[
          <Button key="close" type="primary" onClick={() => setSyncModalVisible(false)}>
            关闭
          </Button>,
        ]}
      >
        {syncLoading ? (
          <div style={{ textAlign: 'center', padding: '20px 0' }}>
            <Spin tip="正在同步腾讯云服务器..." />
          </div>
        ) : syncResult ? (
          syncResult.error ? (
            <Alert
              message="同步失败"
              description={syncResult.error}
              type="error"
              showIcon
            />
          ) : (
            <div>
              <Alert
                message="同步成功"
                description={syncResult.message || '腾讯云服务器同步完成'}
                type="success"
                showIcon
                style={{ marginBottom: 16 }}
              />
              <div style={{ marginBottom: 16 }}>
                <p><strong>同步统计：</strong></p>
                <p>总记录数：{syncResult.stats?.total || 0}</p>
                <p>新增记录数：{syncResult.stats?.added || 0}</p>
                <p>更新记录数：{syncResult.stats?.updated || 0}</p>
                <p>未变更记录数：{syncResult.stats?.unchanged || 0}</p>
                <p>回收记录数：{syncResult.stats?.recycled || 0}</p>
                <p>错误数：{syncResult.stats?.errors || 0}</p>
              </div>
              {syncResult.stats?.error_details && syncResult.stats.error_details.length > 0 && (
                <div>
                  <p><strong>错误详情：</strong></p>
                  <ul>
                    {syncResult.stats.error_details.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )
        ) : null}
      </Modal>

      {/* 服务器详情模态框 - 修复磁盘信息和可用区显示 */}
      <Modal
        title="服务器详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={700}
      >
        {currentServer ? (
          <div>
            <Descriptions bordered column={2}>
              <Descriptions.Item label="主机名" span={2}>{currentServer.hostname || '-'}</Descriptions.Item>
              <Descriptions.Item label="内网IP">{currentServer.ipv4 || '-'}</Descriptions.Item>
              <Descriptions.Item label="公网IP">{currentServer.public_ip || '-'}</Descriptions.Item>
              <Descriptions.Item label="操作系统">{currentServer.os || '-'}</Descriptions.Item>
              <Descriptions.Item label="CPU">{currentServer.cpu || '-'}</Descriptions.Item>
              <Descriptions.Item label="内存">{currentServer.memory || '-'}</Descriptions.Item>
              <Descriptions.Item label="地域">{currentServer.region || '-'}</Descriptions.Item>
              <Descriptions.Item label="可用区">{currentServer.availability_zone || '-'}</Descriptions.Item>
              <Descriptions.Item label="状态">
                <Tag color={currentServer.server_status === 'online' ? 'green' : 'red'}>
                  {currentServer.server_status === 'online' ? '在线' : '已下线'}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="来源">
                {currentServer.original_source === 'tencent' ? (
                  <Tag color="blue">腾讯云</Tag>
                ) : currentServer.original_source === 'manual' ? (
                  <Tag color="green">手动录入</Tag>
                ) : (
                  <Tag>未知</Tag>
                )}
              </Descriptions.Item>
              <Descriptions.Item label="实例ID">{currentServer.instance_id || '-'}</Descriptions.Item>
              <Descriptions.Item label="VPC ID">{currentServer.vpc_id || '-'}</Descriptions.Item>
              <Descriptions.Item label="子网ID">{currentServer.subnet_id || '-'}</Descriptions.Item>
              <Descriptions.Item label="物理主机ID">{currentServer.physical_host_id || '-'}</Descriptions.Item>
              <Descriptions.Item label="创建时间">{currentServer.create_time || '-'}</Descriptions.Item>
              <Descriptions.Item label="下线时间">{currentServer.offline_time || '-'}</Descriptions.Item>
              <Descriptions.Item label="备注" span={2}>{currentServer.remark || '-'}</Descriptions.Item>
            </Descriptions>
          </div>
        ) : (
          <Empty description="未找到服务器信息" />
        )}
      </Modal>
    </Card>
  );
}

export default ServerQuery;













