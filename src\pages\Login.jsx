import { useState } from 'react';
import { Form, Input, Button, Card, message, Alert } from 'antd';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { UserOutlined, LockOutlined } from '@ant-design/icons';

function Login() {
  const [form] = Form.useForm();
  const { login } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const onFinish = async (values) => {
    setLoading(true);
    setError('');
    try {
      await login(values.username, values.password);
      message.success('登录成功');
      navigate('/');
    } catch (error) {
      setError(error.message || '登录失败，请检查用户名和密码');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '100vh', background: '#f0f2f5' }}>
      <Card 
        title={<div style={{ textAlign: 'center', fontWeight: 'bold', fontSize: '1.5em' }}>和泰基础资源管理系统</div>} 
        style={{ width: 400, boxShadow: '0 4px 12px rgba(0,0,0,0.15)' }}
      >
        {error && (
          <Alert
            message={error}
            type="error"
            showIcon
            style={{ marginBottom: 16 }}
            closable
            onClose={() => setError('')}
          />
        )}
        <Form 
          form={form} 
          onFinish={onFinish} 
          layout="vertical"
          initialValues={{ remember: true }}
        >
          <Form.Item 
            label="登录账户" 
            name="username" 
            rules={[{ required: true, message: '请输入登录账户' }]}
            tooltip="输入您的用户名，本地用户或LDAP用户均可"
          >
            <Input prefix={<UserOutlined />} placeholder="用户名" />
          </Form.Item>
          <Form.Item 
            label="密码" 
            name="password" 
            rules={[{ required: true, message: '请输入密码' }]}
            tooltip="输入您的密码，系统会自动判断本地认证或LDAP认证"
          >
            <Input.Password prefix={<LockOutlined />} placeholder="密码" />
          </Form.Item>
          <Form.Item>
            <Button 
              type="primary" 
              htmlType="submit" 
              loading={loading} 
              style={{ width: '100%' }}
            >
              登录
            </Button>
          </Form.Item>
          <div style={{ textAlign: 'center', color: '#999', fontSize: '12px' }}>
            <p>系统支持本地用户和LDAP用户登录</p>
            <p>首次登录LDAP账户将自动创建用户</p>
          </div>
        </Form>
      </Card>
    </div>
  );
}

export default Login;
