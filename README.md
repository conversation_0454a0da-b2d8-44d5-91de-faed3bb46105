# 和泰基础资源管理系统 (ICMS2-Web)

和泰基础资源管理系统前端项目，使用 React 18、Ant Design 5.x、React Router 6.x 和 Vite 构建。

## 目录

- [项目概述](#项目概述)
- [技术栈](#技术栈)
- [安装与运行](#安装与运行)
- [系统架构](#系统架构)
- [功能模块](#功能模块)
- [菜单导航](#菜单导航)
- [开发指南](#开发指南)
- [调试与部署](#调试与部署)

## 项目概述

和泰基础资源管理系统（ICMS2-Web）是一个用于管理企业 IT 基础设施资源的综合平台。系统提供了对应用、主机、数据库、文件存储、专线和分机号等资源的统一管理，同时集成了用户权限管理和 LDAP 认证功能。

本项目是该系统的前端部分，采用现代化的 React 技术栈开发，提供友好的用户界面和流畅的操作体验。

## 技术栈

- **框架**: React 18
- **UI 组件库**: Ant Design 5.x
- **路由**: React Router 6.x
- **构建工具**: Vite
- **状态管理**: React Context API
- **HTTP 客户端**: 原生 Fetch API
- **样式**: CSS-in-JS

## 安装与运行

### 安装

1. 克隆仓库：
   ```bash
   git clone <your-repo-url>
   ```

2. 进入项目目录：
   ```bash
   cd icms2-web
   ```

3. 安装依赖：
   ```bash
   npm install
   ```

### 运行

启动开发服务器：
```bash
npm run dev
```

访问 http://localhost:5173

### 构建

生成生产环境代码：
```bash
npm run build
```

## 系统架构

系统采用前后端分离架构：

- **前端**: React 单页应用（SPA）
- **后端**: RESTful API 服务
- **认证**: 基于 JWT 的认证机制，支持本地用户和 LDAP 用户

前端项目结构：

```
icms2-web/
├── public/              # 静态资源
├── src/                 # 源代码
│   ├── components/      # 通用组件
│   ├── contexts/        # React Context
│   ├── pages/           # 页面组件
│   ├── routes/          # 路由配置
│   ├── utils/           # 工具函数
│   ├── App.jsx          # 应用入口
│   └── main.jsx         # 渲染入口
├── index.html           # HTML 模板
├── vite.config.js       # Vite 配置
└── package.json         # 项目依赖
```

## 功能模块

系统主要包含以下功能模块：

### 1. 资源查询

提供对企业 IT 资源的综合查询功能：

- **应用查询**: 查询和管理系统中的应用信息
- **主机查询**: 查询和管理服务器及主机信息
- **数据库查询**: 查询和管理数据库资源信息
- **文件存储查询**: 查询和管理文件存储资源
- **专线查询**: 查询和管理专线信息
- **分机号查询**: 查询和管理电话分机号信息
- **日志下载**: 下载系统操作日志
- **用户权限汇总查询**: 查询用户权限分配情况

### 2. 基础数据管理

管理系统基础数据：

- **系统管理**: 管理系统基础配置
- **分机号码管理**: 管理电话分机号资源
- **专线信息管理**: 管理专线资源信息

### 3. 用户管理

管理系统用户和权限：

- **用户查询**: 查询系统用户信息
- **LDAP 配置管理**: 配置和管理 LDAP 连接
- **角色管理**: 创建和管理用户角色
- **用户权限分配**: 为用户分配系统权限

## 菜单导航

系统提供以下主要导航菜单：

- **首页**: 系统欢迎页面
- **资源查询**
  - 应用查询
  - 主机查询
  - 数据库查询
  - 文件存储查询
  - 专线查询
  - 分机号查询
  - 日志下载
  - 用户权限汇总查询
- **基础数据管理**
  - 系统管理
  - 分机号码管理
  - 专线信息管理
- **用户管理**
  - 用户查询
  - LDAP 配置管理
  - 角色管理
  - 用户权限分配

## 开发指南

### 添加新页面

1. 在 `src/pages` 目录下创建新的页面组件
2. 在 `src/routes/index.jsx` 中添加新的路由配置
3. 在 `src/components/Layout.jsx` 中更新菜单项

### 添加新功能

1. 设计 API 接口（与后端开发人员协商）
2. 实现前端界面和交互逻辑
3. 添加错误处理和数据验证
4. 进行测试和优化

### 代码规范

- 使用 ESLint 进行代码检查
- 遵循组件化和模块化原则
- 使用语义化命名
- 添加必要的注释

## 调试与部署

### 调试

- 使用 Chrome 开发者工具（F12）检查 Console 和 Network
- 在 VS Code 中配置调试，参考 launch.json

### 部署

1. 执行构建命令生成生产环境代码：
   ```bash
   npm run build
   ```

2. 将 `dist` 目录下的文件部署到 Web 服务器

3. 配置服务器将所有路由请求重定向到 index.html（SPA 需要）

4. 确保 API 代理配置正确

## 登录

使用以下凭证登录：

- 用户名：admin
- 密码：Admin@123


