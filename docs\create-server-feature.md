# 创建服务器功能文档

## 概述

在应用环境详情的资源信息选项卡中新增了创建服务器的功能，用户可以通过选择网络安全区和填写应用端口来创建新的腾讯云CVM实例。

## 功能特性

### 1. 网络安全区选择
- 用户可以从下拉列表中选择网络安全区
- 下拉列表显示网络安全区的名称和描述
- 支持搜索功能，方便快速定位

### 2. 应用端口配置
- 应用端口为非必填项
- 支持多个端口，用逗号分隔（例如：8080,8443）
- 提供输入提示和格式说明

### 3. 自动化流程
- 创建成功后自动刷新服务器资源列表
- 自动切换到服务器资源选项卡
- 提供创建状态反馈

## 使用方法

### 1. 进入应用环境详情
1. 在应用查询页面点击"查看详情"按钮
2. 切换到"资源信息"选项卡

### 2. 创建服务器
1. 点击"创建服务器"按钮（绿色按钮）
2. 在弹出的模态框中选择网络安全区
3. 可选择性填写应用端口
4. 点击"确定"提交创建请求

### 3. 查看结果
- 创建请求提交后会显示成功消息
- 页面自动切换到服务器资源选项卡
- 新创建的服务器会出现在列表中

## 技术实现

### 1. 前端组件
- **位置**: `src/pages/AppQuery.jsx`
- **新增状态管理**:
  - `createServerModalVisible`: 控制创建服务器模态框显示
  - `netAreas`: 网络安全区列表
  - `selectedNetArea`: 选中的网络安全区
  - `createServerPort`: 应用端口输入

### 2. API接口
- **获取网络安全区**: `GET /api/cvm/net-areas`
- **创建服务器**: `POST /api/cvm/create`

### 3. 请求参数
```json
{
  "application_id": 1,
  "environment_id": 2,
  "app_en_name": "myapp",
  "env_en_name": "prod",
  "net_area_id": 1,
  "server_port": "8080,8081"
}
```

## 界面设计

### 1. 创建服务器按钮
- 位置：资源信息选项卡顶部按钮组
- 样式：绿色背景，服务器图标
- 状态：仅在应用环境在线时可用

### 2. 创建服务器模态框
- 标题：创建服务器
- 宽度：600px
- 包含字段：
  - 网络安全区（必填）
  - 应用端口（可选）

### 3. 网络安全区下拉框
- 显示格式：名称 + 描述
- 支持搜索过滤
- 加载状态指示

## 错误处理

### 1. 前端验证
- 检查是否选择了网络安全区
- 检查应用环境是否在线

### 2. 后端错误处理
- API调用失败时显示错误消息
- 网络错误时提供重试建议

### 3. 用户反馈
- 成功创建时显示确认消息
- 失败时显示具体错误原因

## 注意事项

1. **权限要求**: 需要有效的JWT Token
2. **环境状态**: 仅在应用环境在线时可创建服务器
3. **网络配置**: 系统会根据环境ID和网络安全区ID自动获取网络配置
4. **主机名分配**: 系统自动分配唯一的主机名
5. **资源绑定**: 创建成功的服务器会自动绑定到当前应用环境

## 后续优化建议

1. **状态监控**: 添加创建进度显示
2. **批量创建**: 支持一次创建多台服务器
3. **配置模板**: 预设常用的服务器配置
4. **成本估算**: 显示预估的服务器成本
5. **创建历史**: 记录和查看创建历史

## 相关文档

- [腾讯云虚拟机管理模块 API 文档](../backend_docs/cvm_management_api.md)
- [应用环境管理文档](../backend_docs/application_environment_api.md)
- [Docker部署指南](./docker-deployment.md)
