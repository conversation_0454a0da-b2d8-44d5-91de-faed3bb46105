apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: icms2-web-ingress
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$1
spec:
  rules:
  - host: ${INGRESS_HOST}
    http:
      paths:
      - path: /(.*)
        pathType: Prefix
        backend:
          service:
            name: icms2-web
            port:
              number: 80
