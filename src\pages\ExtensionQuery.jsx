import { useState } from 'react';
import { Form, Input, Button, Card, Table, message, Spin, Alert } from 'antd';

function ExtensionQuery() {
  const [form] = Form.useForm();
  const [result, setResult] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const onFinish = async (values) => {
    const { name, number } = values;
    if (!name && !number) {
      message.error('姓名或分机号码必须填写一项');
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch('/api/chaxun', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: name || '',
          number: number || '',
        }),
      });

      console.log('Response Status:', response.status);
      if (!response.ok) {
        throw new Error(`接口请求失败，状态码：${response.status}`);
      }

      const data = await response.json();
      console.log('API Response:', data);

      if (!data.result) {
        throw new Error('返回数据中缺少 result 字段');
      }

      const resultData = {
        name: data.result.name || '未知姓名',
        number: Array.isArray(data.result.number)
          ? data.result.number
          : data.result.number
          ? [[data.result.number]]
          : [],
      };

      setResult(resultData);
      message.success('查询成功');
    } catch (err) {
      setError(err.message);
      console.error('查询错误:', err);
      message.error(`查询失败：${err.message}`);
    } finally {
      setLoading(false);
      form.resetFields(); // 清空表单
    }
  };

  const columns = [
    { title: '序号', dataIndex: 'index', key: 'index', width: 80 },
    { title: '姓名', dataIndex: 'name', key: 'name', width: 150 },
    { title: '分机号码', dataIndex: 'number', key: 'number', width: 150 },
  ];

  const dataSource = result
    ? result.number.map((num, index) => ({
        key: index,
        index: index + 1,
        name: result.name,
        number: Array.isArray(num) ? num[0] || 'N/A' : num || 'N/A',
      }))
    : [];

  return (
    <Card title="分机号查询" style={{ maxWidth: 800, margin: '0 auto', minHeight: 400 }}>
      <Form form={form} onFinish={onFinish} layout="inline" style={{ marginBottom: 16 }}>
        <Form.Item name="name" rules={[{ required: false }]}>
          <Input placeholder="姓名" style={{ width: 200 }} />
        </Form.Item>
        <Form.Item name="number" rules={[{ required: false }]}>
          <Input placeholder="分机号码" style={{ width: 200 }} />
        </Form.Item>
        <Form.Item>
          <Button type="primary" htmlType="submit" loading={loading}>
            查询
          </Button>
        </Form.Item>
      </Form>

      {loading && (
        <div style={{ textAlign: 'center', margin: '20px 0' }}>
          <Spin tip="查询中..." />
        </div>
      )}

      {error && (
        <Alert
          message="查询失败"
          description={`错误信息：${error}`}
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {result ? (
        <Table
          columns={columns}
          dataSource={dataSource}
          pagination={false}
          bordered
          style={{ background: '#fff' }}
          locale={{ emptyText: '暂无数据' }}
        />
      ) : (
        !loading && !error && (
          <div style={{ textAlign: 'center', color: '#999', marginTop: 20 }}>
            请填写姓名或分机号码进行查询
          </div>
        )
      )}
    </Card>
  );
}

export default ExtensionQuery;
