import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  message,
  Spin,
  Alert,
  Space,
  Breadcrumb,
  Input,
  Button,
  Typography,
  Empty,
  Row,
  Col,
  Tooltip
} from 'antd';
import {
  FolderOutlined,
  FileOutlined,
  DownloadOutlined,
  SearchOutlined,
  HomeOutlined,
  ReloadOutlined,
  ArrowLeftOutlined
} from '@ant-design/icons';
import { formatFileSize, formatDate } from '../utils/formatters';

const { Title } = Typography;

function LogDownload() {
  // 状态管理
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [currentPath, setCurrentPath] = useState('');
  const [folders, setFolders] = useState([]);
  const [files, setFiles] = useState([]);
  const [searchPrefix, setSearchPrefix] = useState('');
  const [breadcrumbItems, setBreadcrumbItems] = useState([{ title: '根目录', path: '' }]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 30,
    showSizeChanger: true,
    pageSizeOptions: ['10', '30', '50', '100'],
  });

  // 初始加载
  useEffect(() => {
    fetchTopLevelFolders();
  }, []);

  // 获取顶级文件夹列表
  const fetchTopLevelFolders = async (prefix = '') => {
    setLoading(true);
    setError(null);
    setCurrentPath('');
    setBreadcrumbItems([{ title: '根目录', path: '' }]);

    try {
      const queryParams = new URLSearchParams();
      if (prefix) {
        queryParams.append('prefix', prefix);
      }

      const response = await fetch(`/api/logs/folders?${queryParams.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `请求失败，状态码：${response.status}`);
      }

      const data = await response.json();
      setFolders(data.folders || []);
      setFiles([]);

      if (data.folders && data.folders.length === 0) {
        message.info('没有找到文件夹');
      }
    } catch (err) {
      setError(err.message);
      console.error('查询错误:', err);
      message.error(`查询失败：${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 获取指定路径下的文件夹和文件列表
  const fetchFolderContents = async (path) => {
    setLoading(true);
    setError(null);

    try {
      // 构建查询参数
      const queryParams = new URLSearchParams({
        path: path
      });

      const response = await fetch(`/api/logs/objects?${queryParams.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `请求失败，状态码：${response.status}`);
      }

      const data = await response.json();
      setFolders(data.folders || []);
      setFiles(data.files || []);
      setCurrentPath(path);

      // 更新面包屑
      updateBreadcrumb(path);

      if (data.folders.length === 0 && data.files.length === 0) {
        message.info('文件夹为空');
      }
    } catch (err) {
      setError(err.message);
      console.error('查询错误:', err);
      message.error(`查询失败：${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 更新面包屑导航
  const updateBreadcrumb = (path) => {
    if (!path) {
      setBreadcrumbItems([{ title: '根目录', path: '' }]);
      return;
    }

    const parts = path.split('/').filter(part => part);
    const items = [{ title: '根目录', path: '' }];

    let currentPath = '';
    parts.forEach((part) => {
      currentPath += part + '/';
      items.push({
        title: part,
        path: currentPath
      });
    });

    setBreadcrumbItems(items);
  };

  // 处理文件夹点击
  const handleFolderClick = (folderPath) => {
    fetchFolderContents(folderPath);
  };

  // 处理面包屑点击
  const handleBreadcrumbClick = (path) => {
    if (path === '') {
      fetchTopLevelFolders();
    } else {
      fetchFolderContents(path);
    }
  };

  // 处理返回上一级
  const handleGoBack = () => {
    if (breadcrumbItems.length <= 1) {
      fetchTopLevelFolders();
      return;
    }

    const previousItem = breadcrumbItems[breadcrumbItems.length - 2];
    handleBreadcrumbClick(previousItem.path);
  };

  // 处理搜索
  const handleSearch = () => {
    fetchTopLevelFolders(searchPrefix);
  };

  // 处理重置搜索
  const handleResetSearch = () => {
    setSearchPrefix('');
    fetchTopLevelFolders();
  };

  // 处理文件下载
  const handleDownload = (filePath) => {
    const downloadUrl = `/api/logs/download?path=${encodeURIComponent(filePath)}`;
    window.open(downloadUrl, '_blank');
  };

  // 文件夹列表列定义
  const folderColumns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Space>
          <FolderOutlined style={{ color: '#1890ff' }} />
          <a onClick={() => handleFolderClick(record.path)}>{text}</a>
        </Space>
      ),
    },
    {
      title: '类型',
      key: 'type',
      render: () => '文件夹',
      width: 100,
    }
  ];

  // 文件列表列定义
  const fileColumns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      render: (text) => (
        <Space>
          <FileOutlined style={{ color: '#52c41a' }} />
          <span>{text}</span>
        </Space>
      ),
    },
    {
      title: '大小',
      dataIndex: 'size',
      key: 'size',
      width: 150,
      render: (size) => formatFileSize(size),
      sorter: (a, b) => a.size - b.size,
    },
    {
      title: '最后修改时间',
      dataIndex: 'last_modified',
      key: 'last_modified',
      width: 200,
      render: (date) => formatDate(date),
      sorter: (a, b) => new Date(a.last_modified) - new Date(b.last_modified),
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_, record) => (
        <Button
          type="primary"
          icon={<DownloadOutlined />}
          size="small"
          onClick={() => handleDownload(record.path)}
        >
          下载
        </Button>
      ),
    },
  ];

  return (
    <Card
      title="日志下载"
      style={{ minHeight: 400 }}
      extra={
        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => currentPath ? fetchFolderContents(currentPath) : fetchTopLevelFolders()}
          >
            刷新
          </Button>
        </Space>
      }
    >
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col xs={24} md={12}>
          <Space>
            <Input
              placeholder="输入文件夹名称前缀搜索"
              value={searchPrefix}
              onChange={(e) => setSearchPrefix(e.target.value)}
              onPressEnter={handleSearch}
              style={{ width: 250 }}
              allowClear
            />
            <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
              搜索
            </Button>
            <Button icon={<ReloadOutlined />} onClick={handleResetSearch}>
              重置
            </Button>
          </Space>
        </Col>
        <Col xs={24} md={12} style={{ textAlign: 'right' }}>
          {currentPath && (
            <Button icon={<ArrowLeftOutlined />} onClick={handleGoBack}>
              返回上一级
            </Button>
          )}
        </Col>
      </Row>

      <Breadcrumb style={{ marginBottom: 16 }}>
        {breadcrumbItems.map((item, index) => (
          <Breadcrumb.Item key={index}>
            {index === 0 ? (
              <Space>
                <HomeOutlined />
                <a onClick={() => handleBreadcrumbClick(item.path)}>{item.title}</a>
              </Space>
            ) : (
              <a onClick={() => handleBreadcrumbClick(item.path)}>{item.title}</a>
            )}
          </Breadcrumb.Item>
        ))}
      </Breadcrumb>

      {error && (
        <Alert
          message="查询失败"
          description={`错误信息：${error}`}
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
          closable
          onClose={() => setError(null)}
        />
      )}

      {loading ? (
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
          <Spin tip="加载中..." />
        </div>
      ) : (
        <>
          {folders.length > 0 && (
            <div style={{ marginBottom: 16 }}>
              <Title level={5}>文件夹</Title>
              <Table
                columns={folderColumns}
                dataSource={folders.map((folder, index) => ({ ...folder, key: index }))}
                pagination={{
                  current: pagination.current,
                  pageSize: pagination.pageSize,
                  total: folders.length,
                  showSizeChanger: pagination.showSizeChanger,
                  pageSizeOptions: pagination.pageSizeOptions,
                  showTotal: (total) => `共 ${total} 条记录`,
                  onChange: (page, pageSize) => {
                    setPagination({
                      ...pagination,
                      current: page,
                      pageSize: pageSize
                    });
                  }
                }}
                size="middle"
              />
            </div>
          )}

          {files.length > 0 && (
            <div>
              <Title level={5}>文件</Title>
              <Table
                columns={fileColumns}
                dataSource={files.map((file, index) => ({ ...file, key: index }))}
                pagination={{
                  current: pagination.current,
                  pageSize: pagination.pageSize,
                  total: files.length,
                  showSizeChanger: pagination.showSizeChanger,
                  pageSizeOptions: pagination.pageSizeOptions,
                  showTotal: (total) => `共 ${total} 条记录`,
                  onChange: (page, pageSize) => {
                    setPagination({
                      ...pagination,
                      current: page,
                      pageSize: pageSize
                    });
                  }
                }}
                size="middle"
              />
            </div>
          )}

          {folders.length === 0 && files.length === 0 && !loading && (
            <Empty description="没有找到文件或文件夹" />
          )}
        </>
      )}
    </Card>
  );
}

export default LogDownload;
