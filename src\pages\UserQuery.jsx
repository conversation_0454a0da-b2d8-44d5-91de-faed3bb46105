import React, { useState, useEffect } from 'react';
import { Table, Card, Button, Input, Form, Space, Tag, message, Modal } from 'antd';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

function UserQuery() {
  const [form] = Form.useForm(); // 添加这一行来定义form
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [userPermissions, setUserPermissions] = useState(null);
  const [permissionsVisible, setPermissionsVisible] = useState(false);
  const navigate = useNavigate();
  const { user } = useAuth();

  const fetchUsers = async (values = {}) => {
    setLoading(true);
    try {
      const username = values.username || '';
      const response = await fetch(`/api/users?username=${username}`, {
        headers: { 'Content-Type': 'application/json' },
      });
      
      if (!response.ok) throw new Error('获取用户列表失败');
      
      const data = await response.json();
      setUsers(data.map((user, index) => ({ ...user, key: user.id, index: index + 1 })));
    } catch (error) {
      console.error('用户查询错误:', error);
      message.error('获取用户列表失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const fetchUserPermissions = async (userId) => {
    try {
      const response = await fetch(`/api/users/${userId}/permissions`, {
        headers: { 'Content-Type': 'application/json' },
      });
      if (!response.ok) throw new Error('获取用户权限失败');
      const data = await response.json();
      setUserPermissions(data);
      setPermissionsVisible(true);
    } catch (error) {
      console.error('获取用户权限错误:', error);
      message.error('获取用户权限详情失败: ' + error.message);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  const handleViewPermissions = (userId) => {
    fetchUserPermissions(userId);
  };

  const columns = [
    { title: '序号', dataIndex: 'index', key: 'index', width: 80 },
    { title: '登录账户', dataIndex: 'username', key: 'username' },
    { title: '用户显示名', dataIndex: 'display_name', key: 'display_name' },
    { 
      title: '角色', 
      dataIndex: 'roles', 
      key: 'roles',
      render: (roles) => {
        if (!roles || !roles.length) return '无角色';
        return roles.map(role => (
          <Tag color="blue" key={role}>
            {role}
          </Tag>
        ));
      }
    },
    { 
      title: '管理员', 
      dataIndex: 'is_local_admin', 
      key: 'is_local_admin',
      render: (isAdmin) => isAdmin ? <Tag color="red">管理员</Tag> : '普通用户'
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button 
            onClick={() => navigate(`/perm-assign?userId=${record.id}`)}
            disabled={!user?.is_local_admin}
          >
            分配角色
          </Button>
          <Button 
            type="primary"
            onClick={() => handleViewPermissions(record.id)}
          >
            查看权限
          </Button>
        </Space>
      ),
    },
  ];

  // 渲染权限详情模态框
  const renderPermissionsModal = () => {
    if (!userPermissions) return null;
    
    return (
      <Modal
        title={`${userPermissions.user?.display_name || '用户'} 的权限详情`}
        open={permissionsVisible}
        onCancel={() => setPermissionsVisible(false)}
        footer={[
          <Button key="close" onClick={() => setPermissionsVisible(false)}>
            关闭
          </Button>
        ]}
        width={700}
      >
        <div style={{ marginBottom: 16 }}>
          <h3>用户信息</h3>
          <p><strong>用户名:</strong> {userPermissions.user?.username}</p>
          <p><strong>显示名:</strong> {userPermissions.user?.display_name}</p>
        </div>
        
        <div style={{ marginBottom: 16 }}>
          <h3>角色</h3>
          {userPermissions.roles?.map(role => (
            <Tag color="blue" key={role.id}>
              {role.role_name}
            </Tag>
          ))}
        </div>
        
        <div>
          <h3>模块与菜单权限</h3>
          {userPermissions.modules?.map(module => (
            <Card 
              key={module.id} 
              title={module.name} 
              style={{ marginBottom: 16 }}
              size="small"
            >
              <div>
                <strong>菜单:</strong>
                <div style={{ marginTop: 8 }}>
                  {module.menus?.map(menu => (
                    <Tag color="green" key={menu.id} style={{ margin: '0 8px 8px 0' }}>
                      {menu.name}
                    </Tag>
                  ))}
                </div>
              </div>
            </Card>
          ))}
        </div>
      </Modal>
    );
  };

  return (
    <Card title="用户查询" style={{ maxWidth: 1000, margin: '0 auto' }}>
      <Form form={form} onFinish={fetchUsers} layout="inline" style={{ marginBottom: 16 }}>
        <Form.Item name="username">
          <Input placeholder="输入登录账户" style={{ width: 200 }} />
        </Form.Item>
        <Form.Item>
          <Button type="primary" htmlType="submit" loading={loading}>
            查询
          </Button>
        </Form.Item>
      </Form>
      <Table 
        columns={columns} 
        dataSource={users} 
        loading={loading} 
        bordered 
        style={{ background: '#fff' }}
        pagination={{ pageSize: 10 }}
      />
      
      {renderPermissionsModal()}
    </Card>
  );
}

export default UserQuery;
