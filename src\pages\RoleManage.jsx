import React, { useState, useEffect } from 'react';
import { Card, Table, Button, Form, Input, message, Modal, Space, Tag, Popconfirm, Tree, Tabs, Checkbox, Collapse } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, TeamOutlined } from '@ant-design/icons';
import { useAuth } from '../contexts/AuthContext';

const { TabPane } = Tabs;
const { Panel } = Collapse;

// 添加更多CSS样式
const styles = `
  .module-menu-container {
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    background: #fff;
    padding: 8px;
    max-height: 500px;
    overflow-y: auto;
  }
  
  .module-container {
    margin-bottom: 16px;
  }
  
  .module-header {
    padding: 8px;
    background: #f5f5f5;
    border-radius: 4px;
    margin-bottom: 8px;
  }
  
  .module-title {
    font-weight: 500;
  }
  
  .menu-list {
    padding-left: 24px;
  }
  
  .menu-item {
    padding: 6px 0;
  }
  
  /* 增加复选框的可点击区域 */
  .ant-checkbox-wrapper {
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.3s;
  }
  
  .ant-checkbox-wrapper:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
  
  /* 树形容器样式 */
  .tree-container {
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    background: #fff;
    padding: 16px;
    max-height: 500px;
    overflow-y: auto;
  }
  
  /* 确保复选框可点击 */
  .ant-tree-checkbox {
    z-index: 2;
  }
`;

function RoleManage() {
  const { isLocalAdmin } = useAuth();
  const [roles, setRoles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isUsersModalVisible, setIsUsersModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [moduleMenus, setModuleMenus] = useState([]);
  const [selectedRole, setSelectedRole] = useState(null);
  const [roleUsers, setRoleUsers] = useState([]);
  const [roleUsersLoading, setRoleUsersLoading] = useState(false);
  const [allMenusSelected, setAllMenusSelected] = useState(false);

  const fetchRoles = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/roles', {
        headers: { 'Content-Type': 'application/json' },
      });
      if (!response.ok) throw new Error('获取角色列表失败');
      const data = await response.json();
      setRoles(data.map((role, index) => ({ ...role, key: role.id, index: index + 1 })));
    } catch (error) {
      console.error('角色查询错误:', error);
      message.error('获取角色列表失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const fetchModuleMenus = async () => {
    try {
      const response = await fetch('/api/module-menus');
      if (!response.ok) throw new Error('获取模块和菜单失败');
      const data = await response.json();
      setModuleMenus(data);
    } catch (error) {
      console.error('获取模块和菜单失败:', error);
      message.error('获取模块和菜单数据失败: ' + error.message);
    }
  };

  const fetchRoleUsers = async (roleId) => {
    setRoleUsersLoading(true);
    try {
      const response = await fetch(`/api/roles/${roleId}/users`);
      if (!response.ok) throw new Error('获取角色用户列表失败');
      const data = await response.json();
      setRoleUsers(data.map((user, index) => ({ ...user, key: user.id, index: index + 1 })));
    } catch (error) {
      console.error('获取角色用户错误:', error);
      message.error('获取角色用户列表失败: ' + error.message);
    } finally {
      setRoleUsersLoading(false);
    }
  };

  useEffect(() => {
    fetchRoles();
    fetchModuleMenus();
  }, []);

  // 监听表单值变化，更新全选状态
  useEffect(() => {
    const formValues = form.getFieldsValue();
    const menuIds = formValues.menu_ids || [];
    
    // 检查是否所有菜单都被选中
    const allMenusCount = moduleMenus.reduce((count, module) => 
      count + (module.menus?.length || 0), 0);
    setAllMenusSelected(menuIds.length === allMenusCount && menuIds.length > 0);
  }, [form.getFieldsValue(), moduleMenus]);

  const handleEdit = async (role) => {
    setSelectedRole(role);
    
    try {
      const response = await fetch(`/api/roles/${role.id}`);
      if (!response.ok) throw new Error('获取角色详情失败');
      const roleDetail = await response.json();
      
      form.setFieldsValue({
        role_name: roleDetail.role_name,
        description: roleDetail.description,
        menu_ids: roleDetail.menu_ids || [],
      });
      
      setIsModalVisible(true);
    } catch (error) {
      console.error('获取角色详情错误:', error);
      message.error('获取角色详情失败: ' + error.message);
    }
  };

  const handleViewUsers = (role) => {
    setSelectedRole(role);
    fetchRoleUsers(role.id);
    setIsUsersModalVisible(true);
  };

  const handleAdd = () => {
    setSelectedRole(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleDelete = async (roleId) => {
    try {
      const response = await fetch(`/api/roles/${roleId}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        const data = await response.json();
        if (data.error && data.user_count) {
          Modal.error({
            title: '无法删除角色',
            content: (
              <div>
                <p>{data.error}</p>
                <p>该角色已分配给 {data.user_count} 个用户，包括：</p>
                <ul>
                  {data.users.map(user => (
                    <li key={user.id}>{user.display_name} ({user.username})</li>
                  ))}
                </ul>
                {data.has_more && <p>还有更多用户...</p>}
              </div>
            ),
          });
          return;
        }
        throw new Error('删除角色失败');
      }
      
      message.success('角色删除成功');
      fetchRoles();
    } catch (error) {
      console.error('删除角色错误:', error);
      message.error('删除角色失败: ' + error.message);
    }
  };

  const onFinish = async (values) => {
    const method = selectedRole ? 'PUT' : 'POST';
    const url = selectedRole ? `/api/roles/${selectedRole.id}` : '/api/roles';
    
    // 根据选择的菜单自动计算模块ID
    const menuIds = values.menu_ids || [];
    const moduleIds = [];
    
    // 遍历所有模块，如果该模块下有被选中的菜单，则将该模块ID加入moduleIds
    moduleMenus.forEach(module => {
      const moduleMenuIds = module.menus?.map(menu => menu.id) || [];
      const hasSelectedMenu = moduleMenuIds.some(menuId => menuIds.includes(menuId));
      if (hasSelectedMenu) {
        moduleIds.push(module.id);
      }
    });
    
    try {
      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          role_name: values.role_name,
          description: values.description,
          module_ids: moduleIds,
          menu_ids: menuIds,
        }),
      });
      
      if (!response.ok) throw new Error('保存角色失败');
      
      message.success(`角色${selectedRole ? '更新' : '创建'}成功`);
      fetchRoles();
      setIsModalVisible(false);
    } catch (error) {
      message.error(`角色${selectedRole ? '更新' : '创建'}失败: ` + error.message);
    }
  };

  // 渲染菜单选择界面
  const renderMenuSelection = () => {
    // 获取当前选中的菜单ID
    const selectedMenuIds = form.getFieldValue('menu_ids') || [];
    
    // 计算所有菜单的总数
    const allMenuIds = moduleMenus.flatMap(module => 
      module.menus?.map(menu => menu.id) || []
    );
    
    // 判断是否全选
    const isAllSelected = allMenuIds.length > 0 && 
      selectedMenuIds.length === allMenuIds.length;
    
    return (
      <div>
        <Button 
          type="primary" 
          onClick={() => {
            if (isAllSelected) {
              // 如果当前是全选状态，则清空选择
              form.setFieldsValue({ menu_ids: [] });
            } else {
              // 否则选择所有菜单
              form.setFieldsValue({ menu_ids: [...allMenuIds] });
            }
            // 强制更新表单
            form.validateFields(['menu_ids']);
          }}
          style={{ marginBottom: 16 }}
        >
          {isAllSelected ? '取消全选' : '全选'}
        </Button>
        
        <div style={{ 
          border: '1px solid #f0f0f0', 
          borderRadius: '4px', 
          padding: '16px', 
          maxHeight: '500px', 
          overflowY: 'auto',
          background: '#fff'
        }}>
          {moduleMenus.map(module => (
            <div key={module.id} style={{ marginBottom: '20px' }}>
              <div style={{ 
                background: '#f5f5f5', 
                padding: '8px', 
                borderRadius: '4px', 
                marginBottom: '8px' 
              }}>
                <Checkbox 
                  checked={
                    module.menus?.length > 0 && 
                    module.menus.every(menu => selectedMenuIds.includes(menu.id))
                  }
                  onChange={(e) => {
                    const moduleMenuIds = module.menus?.map(menu => menu.id) || [];
                    let newSelectedIds = [...selectedMenuIds];
                    
                    if (e.target.checked) {
                      // 添加该模块的所有菜单
                      moduleMenuIds.forEach(id => {
                        if (!newSelectedIds.includes(id)) {
                          newSelectedIds.push(id);
                        }
                      });
                    } else {
                      // 移除该模块的所有菜单
                      newSelectedIds = newSelectedIds.filter(id => !moduleMenuIds.includes(id));
                    }
                    
                    form.setFieldsValue({ menu_ids: newSelectedIds });
                    form.validateFields(['menu_ids']);
                  }}
                >
                  <span style={{ fontWeight: 'bold' }}>{module.display_name}</span>
                </Checkbox>
              </div>
              
              <div style={{ paddingLeft: '24px' }}>
                {module.menus?.map(menu => (
                  <div key={menu.id} style={{ marginBottom: '8px' }}>
                    <Checkbox 
                      checked={selectedMenuIds.includes(menu.id)}
                      onChange={(e) => {
                        let newSelectedIds = [...selectedMenuIds];
                        
                        if (e.target.checked) {
                          // 添加菜单
                          if (!newSelectedIds.includes(menu.id)) {
                            newSelectedIds.push(menu.id);
                          }
                        } else {
                          // 移除菜单
                          newSelectedIds = newSelectedIds.filter(id => id !== menu.id);
                        }
                        
                        form.setFieldsValue({ menu_ids: newSelectedIds });
                        form.validateFields(['menu_ids']);
                      }}
                    >
                      {menu.menu_display_name}
                    </Checkbox>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const columns = [
    { title: '序号', dataIndex: 'index', key: 'index', width: 60 },
    { title: '角色名称', dataIndex: 'role_name', key: 'role_name' },
    { title: '角色描述', dataIndex: 'description', key: 'description' },
    { 
      title: '模块权限', 
      dataIndex: 'modules', 
      key: 'modules', 
      render: (modules) => {
        if (!modules || !modules.length) return '无权限';
        return modules.map(module => (
          <Tag color="blue" key={module.id}>
            {module.name}
          </Tag>
        ));
      }
    },
    {
      title: '操作',
      key: 'action',
      width: 240,
      render: (_, record) => (
        <Space>
          <Button 
            type="primary" 
            icon={<EditOutlined />} 
            size="small" 
            onClick={() => handleEdit(record)}
            disabled={!isLocalAdmin()}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除此角色吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
            disabled={!isLocalAdmin()}
          >
            <Button 
              danger 
              icon={<DeleteOutlined />} 
              size="small"
              disabled={!isLocalAdmin()}
            >
              删除
            </Button>
          </Popconfirm>
          <Button 
            icon={<TeamOutlined />} 
            size="small"
            onClick={() => handleViewUsers(record)}
          >
            查看用户
          </Button>
        </Space>
      ),
    },
  ];

  const userColumns = [
    { title: '序号', dataIndex: 'index', key: 'index', width: 60 },
    { title: '用户名', dataIndex: 'username', key: 'username' },
    { title: '显示名', dataIndex: 'display_name', key: 'display_name' },
    { title: '邮箱', dataIndex: 'email', key: 'email' },
    { 
      title: '状态', 
      dataIndex: 'is_active', 
      key: 'is_active',
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '激活' : '禁用'}
        </Tag>
      )
    },
  ];

  // 添加菜单项变化处理函数
  const handleMenuChange = (menuId, checked) => {
    const currentMenuIds = [...(form.getFieldValue('menu_ids') || [])];
    
    let newMenuIds;
    if (checked) {
      newMenuIds = [...currentMenuIds, menuId];
    } else {
      newMenuIds = currentMenuIds.filter(id => id !== menuId);
    }
    
    form.setFieldsValue({ menu_ids: newMenuIds });
    // 强制表单验证以触发值更新
    form.validateFields(['menu_ids']);
  };

  // 添加模块全选/取消全选函数
  const handleModuleSelectAll = (moduleId, checked) => {
    // 获取当前选中的菜单ID
    const currentMenuIds = [...(form.getFieldValue('menu_ids') || [])];
    
    // 获取该模块下所有菜单ID
    const module = moduleMenus.find(m => m.id === moduleId);
    if (!module || !module.menus) return;
    
    const moduleMenuIds = module.menus.map(menu => menu.id);
    
    // 根据勾选状态添加或移除菜单ID
    let newMenuIds;
    if (checked) {
      // 添加该模块下所有菜单（确保不重复）
      const uniqueMenuIds = new Set([...currentMenuIds, ...moduleMenuIds]);
      newMenuIds = Array.from(uniqueMenuIds);
    } else {
      // 移除该模块下所有菜单
      newMenuIds = currentMenuIds.filter(id => !moduleMenuIds.includes(id));
    }
    
    // 更新表单值
    form.setFieldsValue({ menu_ids: newMenuIds });
  };

  // 添加全选/取消全选函数
  const handleSelectAll = () => {
    const currentMenuIds = form.getFieldValue('menu_ids') || [];
    
    const allMenuIds = moduleMenus.flatMap(module => 
      module.menus?.map(menu => menu.id) || []
    );
    
    const isAllSelected = allMenuIds.length > 0 && 
      currentMenuIds.length === allMenuIds.length &&
      allMenuIds.every(id => currentMenuIds.includes(id));
    
    if (isAllSelected) {
      form.setFieldsValue({ menu_ids: [] });
    } else {
      form.setFieldsValue({ menu_ids: [...allMenuIds] });
    }
    
    // 强制表单验证以触发值更新
    form.validateFields(['menu_ids']);
  };

  return (
    <Card title="角色管理" style={{ width: '100%', margin: '0 auto' }}>
      <Button 
        type="primary" 
        icon={<PlusOutlined />} 
        onClick={handleAdd} 
        style={{ marginBottom: 16 }}
        disabled={!isLocalAdmin()}
      >
        新增角色
      </Button>
      
      <Table 
        columns={columns} 
        dataSource={roles} 
        loading={loading} 
        bordered 
        style={{ background: '#fff' }}
        pagination={{ pageSize: 10 }}
        scroll={{ x: 800 }}
      />
      
      {/* 角色编辑模态框 */}
      <Modal
        title={selectedRole ? '编辑角色' : '新增角色'}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
        width={700}
        forceRender={true}
      >
        <Form 
          form={form} 
          onFinish={onFinish} 
          layout="vertical"
          onValuesChange={() => {
            // 强制组件重新渲染
            setAllMenusSelected(prev => !prev); // 切换一个状态值触发重新渲染
            setAllMenusSelected(prev => !prev); // 切换回来，保持原状态
          }}
        >
          <Form.Item 
            label="角色名称" 
            name="role_name" 
            rules={[{ required: true, message: '请输入角色名称' }]}
          >
            <Input placeholder="请输入角色名称" />
          </Form.Item>
          
          <Form.Item 
            label="角色描述" 
            name="description"
          >
            <Input.TextArea placeholder="请输入角色描述" rows={2} />
          </Form.Item>
          
          <Form.Item label="菜单权限分配">
            <Form.Item name="menu_ids" hidden>
              <Input />
            </Form.Item>
            
            <Button 
              type="primary" 
              onClick={handleSelectAll}
              style={{ marginBottom: 16 }}
            >
              {allMenusSelected ? '取消全选' : '全选'}
            </Button>
            
            <div style={{ 
              border: '1px solid #f0f0f0', 
              borderRadius: '4px', 
              padding: '16px', 
              maxHeight: '500px', 
              overflowY: 'auto',
              background: '#fff'
            }}>
              {moduleMenus.map(module => (
                <div key={module.id} style={{ marginBottom: '20px' }}>
                  <div style={{ 
                    background: '#f5f5f5', 
                    padding: '8px', 
                    borderRadius: '4px', 
                    marginBottom: '8px' 
                  }}>
                    <Checkbox 
                      checked={
                        module.menus?.length > 0 && 
                        module.menus.every(menu => 
                          (form.getFieldValue('menu_ids') || []).includes(menu.id)
                        )
                      }
                      onChange={(e) => {
                        const moduleMenuIds = module.menus?.map(menu => menu.id) || [];
                        let newSelectedIds = [...(form.getFieldValue('menu_ids') || [])];
                        
                        if (e.target.checked) {
                          // 添加该模块的所有菜单
                          moduleMenuIds.forEach(id => {
                            if (!newSelectedIds.includes(id)) {
                              newSelectedIds.push(id);
                            }
                          });
                        } else {
                          // 移除该模块的所有菜单
                          newSelectedIds = newSelectedIds.filter(id => !moduleMenuIds.includes(id));
                        }
                        
                        form.setFieldsValue({ menu_ids: newSelectedIds });
                        form.validateFields(['menu_ids']);
                      }}
                    >
                      <span style={{ fontWeight: 'bold' }}>{module.display_name}</span>
                    </Checkbox>
                  </div>
                  
                  <div style={{ paddingLeft: '24px' }}>
                    {module.menus?.map(menu => (
                      <div key={menu.id} style={{ marginBottom: '8px' }}>
                        <Checkbox 
                          checked={(form.getFieldValue('menu_ids') || []).includes(menu.id)}
                          onChange={(e) => handleMenuChange(menu.id, e.target.checked)}
                        >
                          {menu.menu_display_name}
                        </Checkbox>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                保存
              </Button>
              <Button onClick={() => setIsModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
      
      {/* 角色用户列表模态框 */}
      <Modal
        title={`角色 "${selectedRole?.role_name || ''}" 的用户列表`}
        open={isUsersModalVisible}
        onCancel={() => setIsUsersModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setIsUsersModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        <Table 
          columns={userColumns} 
          dataSource={roleUsers} 
          loading={roleUsersLoading}
          bordered
          pagination={{ pageSize: 10 }}
          scroll={{ x: 800 }}
        />
      </Modal>
    </Card>
  );
}

export default RoleManage;
