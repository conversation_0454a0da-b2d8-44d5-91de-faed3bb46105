import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Button,
  Card,
  Table,
  message,
  Spin,
  Alert,
  Space,
  Tooltip,
  Tag,
  Pagination,
  Row,
  Col,
  Modal,
  Popconfirm,
  Descriptions,
  Empty,
  Tabs
} from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
  CloudSyncOutlined,
  EyeOutlined,
  LinkOutlined
} from '@ant-design/icons';

function LoadBalancerQuery() {
  const [form] = Form.useForm();
  const [backendIpForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [loadBalancers, setLoadBalancers] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
    showSizeChanger: true,
    pageSizeOptions: ['10', '20', '50', '100'],
  });
  const [syncLoading, setSyncLoading] = useState(false);
  const [syncResult, setSyncResult] = useState(null);
  const [syncModalVisible, setSyncModalVisible] = useState(false);
  const [currentLoadBalancer, setCurrentLoadBalancer] = useState(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [backendServers, setBackendServers] = useState([]);
  const [backendLoading, setBackendLoading] = useState(false);
  const [backendError, setBackendError] = useState(null);
  const [backendIpLoading, setBackendIpLoading] = useState(false);
  const [backendIpResults, setBackendIpResults] = useState([]);
  const [backendIpModalVisible, setBackendIpModalVisible] = useState(false);

  // 表格列定义
  const columns = [
    {
      title: '负载均衡ID',
      dataIndex: 'loadbalancer_id',
      key: 'loadbalancer_id',
      ellipsis: true,
      render: (text, record) => (
        <Button
          type="link"
          onClick={() => handleViewBackends(record)}
          style={{ padding: 0 }}
        >
          {text}
        </Button>
      ),
    },
    {
      title: '负载均衡名称',
      dataIndex: 'lb_name',
      key: 'lb_name',
      ellipsis: true,
    },
    {
      title: 'VIP地址',
      dataIndex: 'vip',
      key: 'vip',
    },
    {
      title: '类型',
      dataIndex: 'lb_type',
      key: 'lb_type',
      render: (type) => {
        const typeMap = {
          1: '通用型负载均衡',
          0: '传统型负载均衡',
        };
        return typeMap[type] || '未知';
      },
    },
    {
      title: '地域',
      dataIndex: 'zone_region',
      key: 'zone_region',
    },
    {
      title: '状态',
      dataIndex: 'lb_status',
      key: 'lb_status',
      width: 100,
      render: (status) => (
        <Tag color={status === 'online' ? 'green' : 'red'}>
          {status === 'online' ? '在线' : '已下线'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record) => (
        <Button
          type="primary"
          icon={<EyeOutlined />}
          size="small"
          onClick={() => handleViewDetail(record)}
        >
          详情
        </Button>
      ),
    },
  ];

  // 初始加载
  useEffect(() => {
    fetchLoadBalancers();
  }, []);

  // 获取负载均衡列表
  const fetchLoadBalancers = async (params = {}) => {
    setLoading(true);
    setError(null);

    try {
      // 构建查询参数
      const queryParams = new URLSearchParams({
        page: params.page || pagination.current,
        per_page: params.pageSize || pagination.pageSize,
        name: params.name || form.getFieldValue('name') || '',
        vip: params.vip || form.getFieldValue('vip') || '',
      });

      const response = await fetch(`/api/load-balancers?${queryParams.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`请求失败，状态码：${response.status}`);
      }

      const data = await response.json();

      // 处理返回数据
      setLoadBalancers(data.items || []);
      setPagination({
        ...pagination,
        current: data.pagination?.page || 1,
        pageSize: data.pagination?.per_page || 20,
        total: data.pagination?.total || 0,
      });

      if (data.items && data.items.length === 0 && data.pagination?.total > 0) {
        message.info('没有找到匹配的负载均衡记录');
      }
    } catch (err) {
      setError(err.message);
      console.error('查询错误:', err);
      message.error(`查询失败：${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 处理表单提交
  const handleSearch = (values) => {
    fetchLoadBalancers({
      ...values,
      page: 1, // 重置到第一页
    });
  };

  // 处理重置
  const handleReset = () => {
    form.resetFields();
    fetchLoadBalancers({ page: 1 });
  };

  // 处理分页变化
  const handleTableChange = (pagination, filters, sorter) => {
    fetchLoadBalancers({
      page: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  // 同步腾讯云负载均衡
  const handleSyncTencentCloud = async () => {
    setSyncLoading(true);
    setSyncResult(null);
    setSyncModalVisible(true);

    try {
      const response = await fetch('/api/load-balancers/sync-tencent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({}), // 发送空的JSON对象，确保Content-Type生效
      });

      // 使用工具函数处理响应
      const { handleApiResponse } = await import('../utils/errorHandler');
      const data = await handleApiResponse(response);

      if (data) {
        setSyncResult(data);
        message.success('腾讯云负载均衡同步完成');
        fetchLoadBalancers(); // 刷新列表
      } else {
        throw new Error('同步腾讯云负载均衡失败：未收到有效响应');
      }
    } catch (err) {
      console.error('同步腾讯云负载均衡错误:', err);
      message.error(`同步腾讯云负载均衡失败: ${err.message}`);
      setSyncResult({ error: err.message });
    } finally {
      setSyncLoading(false);
    }
  };

  // 查看负载均衡详情
  const handleViewDetail = async (loadBalancer) => {
    setCurrentLoadBalancer(loadBalancer);
    setDetailModalVisible(true);

    // 同时获取后端服务信息
    await fetchBackendServers(loadBalancer);
  };

  // 获取后端服务信息
  const fetchBackendServers = async (loadBalancer) => {
    setBackendLoading(true);
    setBackendError(null);
    setBackendServers([]);

    try {
      const region = loadBalancer.zone_region;
      const lbId = loadBalancer.loadbalancer_id;

      const response = await fetch(`/api/load-balancers/${region}/${lbId}/backends`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`请求失败，状态码：${response.status}`);
      }

      const data = await response.json();
      setBackendServers(data.backends || []);
    } catch (err) {
      setBackendError(err.message);
      console.error('获取后端服务错误:', err);
      message.error(`获取后端服务失败：${err.message}`);
    } finally {
      setBackendLoading(false);
    }
  };

  // 查看负载均衡后端服务
  const handleViewBackends = async (loadBalancer) => {
    setCurrentLoadBalancer(loadBalancer);
    setDetailModalVisible(true);

    // 使用提取的函数获取后端服务
    await fetchBackendServers(loadBalancer);
  };

  // 通过后端IP查询负载均衡
  const handleSearchByBackendIp = async () => {
    try {
      const values = await backendIpForm.validateFields();
      const ip = values.backendIp;

      if (!ip) {
        message.warning('请输入后端IP地址');
        return;
      }

      setBackendIpLoading(true);
      setBackendIpResults([]);
      setBackendIpModalVisible(true);

      const response = await fetch(`/api/load-balancers/by-backend-ip?ip=${ip}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`请求失败，状态码：${response.status}`);
      }

      const data = await response.json();
      setBackendIpResults(data.load_balancers || []);

      if (data.load_balancers && data.load_balancers.length === 0) {
        message.info(`未找到与IP ${ip} 关联的负载均衡`);
      }
    } catch (err) {
      console.error('通过后端IP查询错误:', err);
      message.error(`查询失败：${err.message}`);
    } finally {
      setBackendIpLoading(false);
    }
  };

  return (
    <Card
      title="负载均衡查询"
      style={{ minHeight: 400 }}
      extra={
        <Button
          type="default"
          icon={<CloudSyncOutlined />}
          onClick={handleSyncTencentCloud}
          loading={syncLoading}
        >
          同步腾讯云负载均衡
        </Button>
      }
    >
      <Row gutter={16}>
        <Col xs={24} md={16}>
          <Form
            form={form}
            name="loadBalancerQueryForm"
            onFinish={handleSearch}
            layout="vertical"
          >
            <Row gutter={16}>
              <Col xs={24} sm={12} md={8}>
                <Form.Item name="name" label="负载均衡名称">
                  <Input placeholder="输入负载均衡名称关键字" allowClear />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Form.Item name="vip" label="VIP地址">
                  <Input placeholder="输入VIP地址关键字" allowClear />
                </Form.Item>
              </Col>
            </Row>
            <Row>
              <Col>
                <Space>
                  <Button type="primary" htmlType="submit" icon={<SearchOutlined />} loading={loading}>
                    查询
                  </Button>
                  <Button icon={<ReloadOutlined />} onClick={handleReset}>
                    重置
                  </Button>
                </Space>
              </Col>
            </Row>
          </Form>
        </Col>
        <Col xs={24} md={8}>
          <Form
            form={backendIpForm}
            name="backendIpForm"
            onFinish={handleSearchByBackendIp}
            layout="vertical"
          >
            <Form.Item name="backendIp" label="通过后端IP查询负载均衡">
              <Input placeholder="输入后端服务器IP地址" allowClear />
            </Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={backendIpLoading}
              style={{ marginTop: '0', marginLeft: '0' }}
            >
              查询
            </Button>
          </Form>
        </Col>
      </Row>

      {error && (
        <Alert
          message="查询失败"
          description={`错误信息：${error}`}
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
          closable
          onClose={() => setError(null)}
        />
      )}

      <Table
        columns={columns}
        dataSource={loadBalancers.map((lb, index) => ({ ...lb, key: lb.id || index }))}
        pagination={false}
        loading={loading}
        onChange={handleTableChange}
        scroll={{ x: 'max-content' }}
        size="middle"
      />

      <div style={{ marginTop: 16, textAlign: 'right' }}>
        <Pagination
          current={pagination.current}
          pageSize={pagination.pageSize}
          total={pagination.total}
          showSizeChanger
          showQuickJumper
          showTotal={(total) => `共 ${total} 条记录`}
          onChange={(page, pageSize) => fetchLoadBalancers({ page, pageSize })}
          onShowSizeChange={(current, size) => fetchLoadBalancers({ page: 1, pageSize: size })}
        />
      </div>

      {/* 同步结果弹窗 */}
      <Modal
        title="同步腾讯云负载均衡"
        open={syncModalVisible}
        onCancel={() => setSyncModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setSyncModalVisible(false)}>
            关闭
          </Button>,
        ]}
        width={600}
      >
        {syncLoading ? (
          <div style={{ textAlign: 'center', padding: '20px 0' }}>
            <Spin tip="正在同步腾讯云负载均衡..." />
          </div>
        ) : syncResult ? (
          syncResult.error ? (
            <Alert
              message="同步失败"
              description={syncResult.error}
              type="error"
              showIcon
            />
          ) : (
            <div>
              <Alert
                message="同步成功"
                description={syncResult.message || '腾讯云负载均衡同步完成'}
                type="success"
                showIcon
                style={{ marginBottom: 16 }}
              />
              <div style={{ marginBottom: 16 }}>
                <p><strong>同步统计：</strong></p>
                <p>总记录数：{syncResult.stats?.total || 0}</p>
                <p>新增记录数：{syncResult.stats?.added || 0}</p>
                <p>更新记录数：{syncResult.stats?.updated || 0}</p>
                <p>未变更记录数：{syncResult.stats?.unchanged || 0}</p>
                <p>回收记录数：{syncResult.stats?.recycled || 0}</p>
                <p>错误数：{syncResult.stats?.errors || 0}</p>
              </div>
              {syncResult.stats?.error_details && syncResult.stats.error_details.length > 0 && (
                <div>
                  <p><strong>错误详情：</strong></p>
                  <ul>
                    {syncResult.stats.error_details.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )
        ) : null}
      </Modal>

      {/* 负载均衡详情弹窗 */}
      <Modal
        title="负载均衡详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>,
        ]}
        width={800}
      >
        {currentLoadBalancer ? (
          <Tabs defaultActiveKey={backendServers.length > 0 ? "backends" : "basic"} items={[
            {
              key: 'basic',
              label: '基本信息',
              children: (
                <Descriptions bordered column={2}>
                  <Descriptions.Item label="负载均衡ID" span={2}>{currentLoadBalancer.loadbalancer_id || '-'}</Descriptions.Item>
                  <Descriptions.Item label="负载均衡名称" span={2}>{currentLoadBalancer.lb_name || '-'}</Descriptions.Item>
                  <Descriptions.Item label="VIP地址">{currentLoadBalancer.vip || '-'}</Descriptions.Item>
                  <Descriptions.Item label="IP版本">{currentLoadBalancer.ip_version === 'ipv4' ? 'IPv4' : 'IPv6'}</Descriptions.Item>
                  <Descriptions.Item label="状态">
                    <Tag color={currentLoadBalancer.lb_status === 'online' ? 'green' : 'red'}>
                      {currentLoadBalancer.lb_status === 'online' ? '在线' : '已下线'}
                    </Tag>
                  </Descriptions.Item>
                  <Descriptions.Item label="类型">
                    {currentLoadBalancer.lb_type === 1 ? '通用型负载均衡' : currentLoadBalancer.lb_type === 0 ? '传统型负载均衡' : '未知'}
                  </Descriptions.Item>
                  <Descriptions.Item label="所属网络">{currentLoadBalancer.belong_network || '-'}</Descriptions.Item>
                  <Descriptions.Item label="地域">{currentLoadBalancer.zone_region || '-'}</Descriptions.Item>
                  <Descriptions.Item label="创建时间">{currentLoadBalancer.create_time || '-'}</Descriptions.Item>
                  <Descriptions.Item label="下线时间">{currentLoadBalancer.offline_time || '-'}</Descriptions.Item>
                  <Descriptions.Item label="来源" span={2}>
                    {currentLoadBalancer.original_source === 'tencent' ? '腾讯云' :
                     currentLoadBalancer.original_source === 'manual' ? '手动添加' :
                     currentLoadBalancer.original_source || '-'}
                  </Descriptions.Item>
                </Descriptions>
              )
            },
            {
              key: 'backends',
              label: '后端服务',
              children: (
                <div>
                  {backendLoading ? (
                    <div style={{ textAlign: 'center', padding: '20px 0' }}>
                      <Spin tip="正在加载后端服务..." />
                    </div>
                  ) : backendError ? (
                    <Alert
                      message="获取后端服务失败"
                      description={`错误信息：${backendError}`}
                      type="error"
                      showIcon
                    />
                  ) : (
                    <Table
                      columns={[
                        { title: '协议', dataIndex: 'protocol', key: 'protocol' },
                        { title: '监听端口', dataIndex: 'listener_port', key: 'listener_port' },
                        { title: '域名', dataIndex: 'domain', key: 'domain',
                          render: (text) => text || '-' },
                        { title: '实例ID', dataIndex: 'instance_id', key: 'instance_id' },
                        { title: 'IP地址', dataIndex: 'ip', key: 'ip' },
                        { title: '后端端口', dataIndex: 'port', key: 'port' },
                        { title: '权重', dataIndex: 'weight', key: 'weight' }
                      ]}
                      dataSource={backendServers.map((server, index) => ({ ...server, key: index }))}
                      pagination={false}
                      size="small"
                      scroll={{ x: 'max-content' }}
                      locale={{ emptyText: '该负载均衡没有后端服务' }}
                    />
                  )}
                </div>
              )
            }
          ]} />
        ) : (
          <Empty description="无法获取负载均衡详情" />
        )}
      </Modal>

      {/* 后端IP查询结果弹窗 */}
      <Modal
        title="后端IP查询结果"
        open={backendIpModalVisible}
        onCancel={() => setBackendIpModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setBackendIpModalVisible(false)}>
            关闭
          </Button>,
        ]}
        width={700}
      >
        {backendIpLoading ? (
          <div style={{ textAlign: 'center', padding: '20px 0' }}>
            <Spin tip="正在查询..." />
          </div>
        ) : (
          <Table
            columns={[
              { title: '负载均衡ID', dataIndex: 'loadbalancer_id', key: 'loadbalancer_id' },
              { title: '负载均衡名称', dataIndex: 'lb_name', key: 'lb_name' },
              { title: '地域', dataIndex: 'region', key: 'region' },
              { title: 'VIP地址', dataIndex: 'vip', key: 'vip' }
            ]}
            dataSource={backendIpResults.map((lb, index) => ({ ...lb, key: index }))}
            pagination={false}
            locale={{ emptyText: '未找到与该IP关联的负载均衡' }}
          />
        )}
      </Modal>
    </Card>
  );
}

export default LoadBalancerQuery;

