import { useState, useEffect } from 'react';
import { Card, Table, Button, Form, Input, InputNumber, Switch, message, Modal, Space, Tag, Popconfirm, Tooltip } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, CheckCircleOutlined, CloseCircleOutlined, SyncOutlined, UserOutlined } from '@ant-design/icons';
import { useAuth } from '../contexts/AuthContext';

function LdapConfig() {
  const { isLocalAdmin } = useAuth();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [ldapConfigs, setLdapConfigs] = useState([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingConfig, setEditingConfig] = useState(null);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [searchUsername, setSearchUsername] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [searchLoading, setSearchLoading] = useState(false);
  const [importLoading, setImportLoading] = useState({});
  const [searchModalVisible, setSearchModalVisible] = useState(false);
  const [testModalVisible, setTestModalVisible] = useState(false);
  const [currentConfigId, setCurrentConfigId] = useState(null);
  const [importResult, setImportResult] = useState(null);
  const [importModalVisible, setImportModalVisible] = useState(false);

  // 获取LDAP配置列表
  const fetchLdapConfigs = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/ldap-configs');
      if (!response.ok) throw new Error('获取LDAP配置失败');
      const data = await response.json();
      setLdapConfigs(data.map((config, index) => ({ ...config, key: config.id, index: index + 1 })));
    } catch (error) {
      console.error('获取LDAP配置错误:', error);
      message.error('获取LDAP配置失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLdapConfigs();
  }, []);

  // 打开创建/编辑模态框
  const showModal = (config = null) => {
    setEditingConfig(config);
    form.resetFields();
    if (config) {
      form.setFieldsValue({
        server: config.server,
        port: config.port,
        bind_dn: config.bind_dn,
        password: '', // 密码不会从服务器返回，需要用户重新输入
        ou_path: config.ou_path,
        is_active: config.is_active
      });
    } else {
      form.setFieldsValue({
        port: 389,
        is_active: true
      });
    }
    setIsModalVisible(true);
  };

  // 处理模态框确认
  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      setConfirmLoading(true);
      
      if (editingConfig) {
        // 更新现有配置
        const response = await fetch(`/api/ldap-config/${editingConfig.id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(values),
        });
        
        if (!response.ok) throw new Error('更新LDAP配置失败');
        
        message.success('LDAP配置更新成功');
      } else {
        // 创建新配置
        const response = await fetch('/api/ldap-config', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(values),
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || '创建LDAP配置失败');
        }
        
        message.success('LDAP配置创建成功');
      }
      
      setIsModalVisible(false);
      fetchLdapConfigs();
    } catch (error) {
      console.error('保存LDAP配置错误:', error);
      message.error('保存LDAP配置失败: ' + error.message);
    } finally {
      setConfirmLoading(false);
    }
  };

  // 删除LDAP配置
  const handleDelete = async (id) => {
    try {
      const response = await fetch(`/api/ldap-config/${id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) throw new Error('删除LDAP配置失败');
      
      message.success('LDAP配置删除成功');
      fetchLdapConfigs();
    } catch (error) {
      console.error('删除LDAP配置错误:', error);
      message.error('删除LDAP配置失败: ' + error.message);
    }
  };

  // 切换LDAP配置激活状态
  const toggleActive = async (id, currentStatus) => {
    try {
      const response = await fetch(`/api/ldap-config/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ is_active: !currentStatus }),
      });
      
      if (!response.ok) throw new Error('更新LDAP配置状态失败');
      
      message.success(`LDAP配置${!currentStatus ? '激活' : '停用'}成功`);
      fetchLdapConfigs();
    } catch (error) {
      console.error('更新LDAP配置状态错误:', error);
      message.error('更新LDAP配置状态失败: ' + error.message);
    }
  };

  // 打开测试用户搜索模态框
  const showTestModal = (configId) => {
    setCurrentConfigId(configId);
    setSearchUsername('');
    setSearchResults([]);
    setTestModalVisible(true);
  };

  // 搜索LDAP用户
  const handleSearch = async () => {
    if (!currentConfigId) {
      message.warning('请先选择LDAP配置');
      return;
    }
    
    if (!searchUsername) {
      message.warning('请输入要搜索的用户名');
      return;
    }
    
    setSearchLoading(true);
    try {
      const response = await fetch('/api/ldap-test-search', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          config_id: currentConfigId,
          username: searchUsername
        }),
      });
      
      if (!response.ok) throw new Error('LDAP用户搜索失败');
      
      const data = await response.json();
      if (data.found) {
        setSearchResults(data.users);
        setSearchModalVisible(true);
        setTestModalVisible(false);
      } else {
        message.info(data.message || '未找到匹配的用户');
        setSearchResults([]);
      }
    } catch (error) {
      console.error('LDAP用户搜索错误:', error);
      message.error('LDAP用户搜索失败: ' + error.message);
    } finally {
      setSearchLoading(false);
    }
  };

  // 导入LDAP用户
  const handleImport = async (configId) => {
    setImportLoading(prev => ({ ...prev, [configId]: true }));
    try {
      const response = await fetch('/api/ldap-fetch-users', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          config_id: configId
        }),
      });
      
      if (!response.ok) throw new Error('LDAP用户导入失败');
      
      const data = await response.json();
      setImportResult(data);
      setImportModalVisible(true);
      message.success(data.message || '用户导入成功');
    } catch (error) {
      console.error('LDAP用户导入错误:', error);
      message.error('LDAP用户导入失败: ' + error.message);
    } finally {
      setImportLoading(prev => ({ ...prev, [configId]: false }));
    }
  };

  // 搜索结果列定义
  const searchColumns = [
    { 
      title: '显示名称', 
      dataIndex: 'displayName', 
      key: 'displayName',
      render: (text) => text || '未设置'
    },
    { 
      title: '用户名', 
      dataIndex: 'sAMAccountName', 
      key: 'sAMAccountName',
      render: (text) => text || '未设置'
    },
    { 
      title: '邮箱', 
      dataIndex: 'mail', 
      key: 'mail',
      render: (text) => text || '未设置'
    },
    { 
      title: '通用名称', 
      dataIndex: 'cn', 
      key: 'cn',
      render: (text) => text || '未设置'
    }
  ];

  // 列定义
  const columns = [
    { title: '序号', dataIndex: 'index', key: 'index', width: 60, fixed: 'left' },
    { 
      title: '服务器', 
      dataIndex: 'server', 
      key: 'server', 
      width: 180, 
      ellipsis: true,
      render: (text) => (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      )
    },
    { title: '端口', dataIndex: 'port', key: 'port', width: 80 },
    { 
      title: '绑定DN', 
      dataIndex: 'bind_dn', 
      key: 'bind_dn', 
      width: 180, 
      ellipsis: true,
      render: (text) => (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      )
    },
    { 
      title: 'OU路径', 
      dataIndex: 'ou_path', 
      key: 'ou_path', 
      width: 180, 
      ellipsis: true,
      render: (text) => (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      )
    },
    { 
      title: '状态', 
      dataIndex: 'is_active', 
      key: 'is_active',
      width: 80,
      render: (active) => (
        <Tag color={active ? 'green' : 'red'}>
          {active ? '已激活' : '已停用'}
        </Tag>
      )
    },
    { 
      title: '创建时间', 
      dataIndex: 'create_time', 
      key: 'create_time',
      width: 160,
      render: (time) => new Date(time).toLocaleString()
    },
    {
      title: '操作',
      key: 'action',
      width: 360,
      fixed: 'right',
      render: (_, record) => (
        <Space wrap>
          <Button 
            type="primary" 
            icon={<EditOutlined />} 
            size="small" 
            onClick={() => showModal(record)}
            disabled={!isLocalAdmin()}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除此LDAP配置吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
            disabled={!isLocalAdmin()}
          >
            <Button 
              danger 
              icon={<DeleteOutlined />} 
              size="small"
              disabled={!isLocalAdmin()}
            >
              删除
            </Button>
          </Popconfirm>
          <Button 
            type={record.is_active ? "default" : "primary"} 
            icon={record.is_active ? <CloseCircleOutlined /> : <CheckCircleOutlined />} 
            size="small"
            onClick={() => toggleActive(record.id, record.is_active)}
            disabled={!isLocalAdmin()}
          >
            {record.is_active ? '停用' : '激活'}
          </Button>
          <Button 
            type="default" 
            icon={<UserOutlined />} 
            size="small"
            onClick={() => showTestModal(record.id)}
          >
            测试
          </Button>
          <Button 
            type="default" 
            icon={<SyncOutlined />} 
            size="small"
            onClick={() => handleImport(record.id)}
            loading={importLoading[record.id]}
            disabled={!isLocalAdmin() || !record.is_active}
          >
            同步用户
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <Card 
      title="LDAP配置管理" 
      extra={
        <Button 
          type="primary" 
          icon={<PlusOutlined />} 
          onClick={() => showModal()}
          disabled={!isLocalAdmin()}
        >
          添加LDAP配置
        </Button>
      }
      style={{ width: '100%', margin: '0 auto' }}
    >
      <Table 
        columns={columns} 
        dataSource={ldapConfigs} 
        loading={loading}
        bordered
        pagination={{ pageSize: 10 }}
        scroll={{ x: 1300 }}
        size="middle"
        rowKey="id"
      />
      <Modal
        title={editingConfig ? "编辑LDAP配置" : "添加LDAP配置"}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        onOk={handleOk}
        confirmLoading={confirmLoading}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="server"
            label="LDAP服务器"
            rules={[{ required: true, message: '请输入LDAP服务器地址' }]}
          >
            <Input placeholder="例如: ldap.example.com" />
          </Form.Item>
          
          <Form.Item
            name="port"
            label="端口"
            rules={[{ required: true, message: '请输入端口号' }]}
          >
            <InputNumber min={1} max={65535} style={{ width: '100%' }} placeholder="例如: 389" />
          </Form.Item>
          
          <Form.Item
            name="bind_dn"
            label="绑定DN"
            rules={[{ required: true, message: '请输入绑定DN' }]}
            tooltip="用于连接LDAP服务器的账户DN"
          >
            <Input placeholder="例如: cn=admin,dc=example,dc=com" />
          </Form.Item>
          
          <Form.Item
            name="password"
            label="绑定密码"
            rules={[
              { 
                required: !editingConfig, 
                message: '请输入绑定密码' 
              },
              {
                required: false,
                message: '如果不修改密码，请留空',
                warningOnly: true,
                type: 'string',
                len: 0,
              }
            ]}
            tooltip={editingConfig ? "如果不修改密码，请留空" : "用于连接LDAP服务器的账户密码"}
          >
            <Input.Password placeholder={editingConfig ? "不修改请留空" : "输入绑定密码"} />
          </Form.Item>
          
          <Form.Item
            name="ou_path"
            label="OU路径"
            rules={[{ required: true, message: '请输入OU路径' }]}
            tooltip="用户所在的组织单位路径"
          >
            <Input placeholder="例如: ou=users,dc=example,dc=com" />
          </Form.Item>
          
          <Form.Item
            name="is_active"
            label="是否激活"
            valuePropName="checked"
          >
            <Switch checkedChildren="激活" unCheckedChildren="停用" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 测试用户搜索模态框 */}
      <Modal
        title="LDAP用户搜索测试"
        open={testModalVisible}
        onCancel={() => setTestModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setTestModalVisible(false)}>
            取消
          </Button>,
          <Button 
            key="search" 
            type="primary" 
            loading={searchLoading} 
            onClick={handleSearch}
            disabled={!searchUsername}
          >
            搜索
          </Button>
        ]}
      >
        <Form layout="vertical">
          <Form.Item 
            label="用户名" 
            required
            tooltip="输入要搜索的用户名，将在LDAP中查找匹配的用户"
          >
            <Input 
              placeholder="输入要搜索的用户名" 
              value={searchUsername} 
              onChange={(e) => setSearchUsername(e.target.value)}
              onPressEnter={handleSearch}
              prefix={<UserOutlined />}
            />
          </Form.Item>
        </Form>
      </Modal>
      
      {/* 搜索结果模态框 */}
      <Modal
        title="LDAP用户搜索结果"
        open={searchModalVisible}
        onCancel={() => setSearchModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setSearchModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        <Table 
          columns={searchColumns} 
          dataSource={searchResults.map((user, index) => ({ ...user, key: index }))} 
          bordered
          pagination={false}
          size="small"
        />
      </Modal>
      
      {/* 导入结果模态框 */}
      <Modal
        title="LDAP用户导入结果"
        open={importModalVisible}
        onCancel={() => setImportModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setImportModalVisible(false)}>
            关闭
          </Button>
        ]}
      >
        {importResult && (
          <div>
            <p>导入结果: {importResult.message}</p>
            <p>新增用户: <Tag color="green">{importResult.added}</Tag></p>
            <p>更新用户: <Tag color="blue">{importResult.updated}</Tag></p>
            <p>总计处理: <Tag color="purple">{importResult.total}</Tag></p>
            <p style={{ marginTop: 16 }}>
              注意: 导入的用户需要单独分配角色才能获得权限。
            </p>
          </div>
        )}
      </Modal>
    </Card>
  );
}

export default LdapConfig;
