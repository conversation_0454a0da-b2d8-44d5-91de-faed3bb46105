# Kubernetes 部署指南

本文档提供了如何在 Kubernetes 集群中部署和泰基础资源管理系统前端（ICMS2-Web）的详细说明，确保与后端部署在同一个集群的同一个命名空间下。

## 目录

- [前提条件](#前提条件)
- [部署流程](#部署流程)
- [配置说明](#配置说明)
- [部署验证](#部署验证)
- [故障排除](#故障排除)

## 前提条件

- 已有 Kubernetes 集群
- 已安装 kubectl 命令行工具
- 已配置 kubectl 可以访问目标集群
- 已有 Docker 镜像仓库访问权限
- 后端服务已部署在集群中

## 部署流程

### 1. 准备环境变量

在部署前，需要设置以下环境变量：

```bash
# Docker镜像仓库地址
export DOCKER_REGISTRY=<your-registry>

# 镜像标签
export IMAGE_TAG=<tag>

# 部署的命名空间（与后端相同）
export NAMESPACE=<namespace>

# Ingress主机名
export INGRESS_HOST=<your-domain>

# 后端API服务地址（集群内服务名）
export API_URL=http://icms2-api-service
```

### 2. 构建并推送Docker镜像

```bash
# 构建镜像
docker build -t ${DOCKER_REGISTRY}/icms2-web:${IMAGE_TAG} .

# 推送镜像到仓库
docker push ${DOCKER_REGISTRY}/icms2-web:${IMAGE_TAG}
```

### 3. 准备Kubernetes配置文件

项目中已包含以下Kubernetes配置文件：

- `k8s/deployment.yaml`: 部署配置
- `k8s/service.yaml`: 服务配置
- `k8s/configmap.yaml`: 配置映射
- `k8s/ingress.yaml`: 入口配置
- `k8s/kustomization.yaml`: Kustomize配置

### 4. 替换配置文件中的变量

使用环境变量替换配置文件中的占位符：

```bash
# 替换kustomization.yaml中的命名空间
envsubst < k8s/kustomization.yaml > k8s/kustomization_processed.yaml
mv k8s/kustomization_processed.yaml k8s/kustomization.yaml

# 替换deployment.yaml中的镜像信息
envsubst < k8s/deployment.yaml > k8s/deployment_processed.yaml
mv k8s/deployment_processed.yaml k8s/deployment.yaml

# 替换configmap.yaml中的API URL
envsubst < k8s/configmap.yaml > k8s/configmap_processed.yaml
mv k8s/configmap_processed.yaml k8s/configmap.yaml

# 替换ingress.yaml中的主机名
envsubst < k8s/ingress.yaml > k8s/ingress_processed.yaml
mv k8s/ingress_processed.yaml k8s/ingress.yaml
```

### 5. 创建镜像拉取凭证（如需要）

如果使用私有镜像仓库，需要创建拉取凭证：

```bash
kubectl create secret docker-registry regcred \
  --docker-server=${DOCKER_REGISTRY} \
  --docker-username=<your-username> \
  --docker-password=<your-password> \
  --docker-email=<your-email> \
  --namespace=${NAMESPACE}
```

### 6. 应用Kubernetes配置

```bash
# 确保命名空间存在
kubectl create namespace ${NAMESPACE} --dry-run=client -o yaml | kubectl apply -f -

# 使用kustomize应用所有配置
kubectl apply -k k8s/
```

## 配置说明

### ConfigMap

ConfigMap用于存储应用配置，主要包含API URL：

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: icms2-web-config
data:
  api_url: "http://icms2-api-service"  # 后端API服务地址
```

### Deployment

Deployment定义了应用的部署配置：

- 副本数：2（可根据需求调整）
- 资源限制：CPU 500m，内存 512Mi
- 健康检查：HTTP GET /
- 环境变量：从ConfigMap获取API_URL

### Service

Service定义了应用的服务配置：

- 类型：ClusterIP（集群内部访问）
- 端口：80

### Ingress

Ingress定义了应用的外部访问入口：

- 主机名：由环境变量INGRESS_HOST指定
- 路径：/(.*)，将所有请求转发到前端服务

## 部署验证

部署完成后，可以通过以下步骤验证部署是否成功：

### 1. 检查Pod状态

```bash
kubectl get pods -n ${NAMESPACE} -l app=icms2-web
```

所有Pod应该处于Running状态，并且Ready列应该显示1/1。

### 2. 检查Service

```bash
kubectl get svc -n ${NAMESPACE} -l app=icms2-web
```

确认Service已创建并分配了ClusterIP。

### 3. 检查Ingress

```bash
kubectl get ingress -n ${NAMESPACE}
```

确认Ingress已创建并分配了ADDRESS。

### 4. 访问应用

通过浏览器访问配置的域名，确认应用可以正常加载。

## 故障排除

### Pod启动失败

检查Pod日志：

```bash
kubectl logs -n ${NAMESPACE} <pod-name>
```

### API连接问题

确认ConfigMap中的API URL配置正确：

```bash
kubectl get configmap -n ${NAMESPACE} icms2-web-config -o yaml
```

### Ingress问题

检查Ingress控制器日志：

```bash
kubectl logs -n ingress-nginx <ingress-controller-pod> -f
```

### 镜像拉取失败

检查Secret是否正确创建：

```bash
kubectl get secret -n ${NAMESPACE} regcred
```

检查Pod事件：

```bash
kubectl describe pod -n ${NAMESPACE} <pod-name>
```
