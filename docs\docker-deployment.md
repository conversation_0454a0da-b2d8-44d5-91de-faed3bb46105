# Docker 部署指南

本文档提供了如何在 Docker 环境中部署和泰基础资源管理系统前端（ICMS2-Web）的详细说明。

## 目录

- [前提条件](#前提条件)
- [构建 Docker 镜像](#构建-docker-镜像)
- [本地测试](#本地测试)
- [Kubernetes 部署](#kubernetes-部署)
- [配置说明](#配置说明)
- [常见问题](#常见问题)

## 前提条件

- Docker 20.10.x 或更高版本
- Kubernetes 集群（如果使用 Kubernetes 部署）
- kubectl 命令行工具
- 访问 Docker 镜像仓库的权限

## 构建 Docker 镜像

1. 在项目根目录下执行以下命令构建 Docker 镜像：

```bash
# 替换 <your-registry> 为您的 Docker 镜像仓库地址
# 替换 <tag> 为您想要的版本标签，例如 v1.0.0 或 latest
docker build -t <your-registry>/icms2-web:<tag> .
```

2. 推送镜像到 Docker 镜像仓库：

```bash
docker push <your-registry>/icms2-web:<tag>
```

## 本地测试

您可以在本地使用以下命令运行容器进行测试：

```bash
docker run -d -p 8080:80 -e API_URL=http://your-backend-api-url <your-registry>/icms2-web:<tag>
```

然后在浏览器中访问 `http://localhost:8080` 查看应用。

## Kubernetes 部署

### 准备工作

1. 确保您已经配置了 kubectl 并可以连接到 Kubernetes 集群。

2. 确保您已经创建了用于拉取私有镜像的 Secret（如果需要）：

```bash
kubectl create secret docker-registry regcred \
  --docker-server=<your-registry> \
  --docker-username=<your-username> \
  --docker-password=<your-password> \
  --docker-email=<your-email> \
  --namespace=<namespace>
```

### 部署应用

1. 修改 `k8s/configmap.yaml` 中的 API URL，确保它指向正确的后端服务：

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: icms2-web-config
data:
  api_url: "http://icms2-api-service"  # 修改为您的后端服务地址
```

2. 修改 `k8s/deployment.yaml` 中的镜像地址和标签：

```yaml
image: <your-registry>/icms2-web:<tag>
```

3. 使用 kustomize 部署应用：

```bash
# 设置环境变量
export DOCKER_REGISTRY=<your-registry>
export IMAGE_TAG=<tag>
export NAMESPACE=<namespace>
export INGRESS_HOST=<your-domain>

# 应用配置
kubectl apply -k k8s/
```

或者，您可以使用 envsubst 替换变量后应用：

```bash
envsubst < k8s/kustomization.yaml | kubectl apply -f -
```

## 配置说明

### Nginx 配置

`nginx.conf` 文件包含了 Nginx 的配置，主要包括：

- 静态资源缓存设置
- API 代理设置
- 单页应用路由处理
- GZIP 压缩设置

### Kubernetes 配置

- `deployment.yaml`: 定义了应用的部署配置，包括副本数、资源限制、健康检查等
- `service.yaml`: 定义了应用的服务配置
- `configmap.yaml`: 存储应用的配置信息，如 API URL
- `ingress.yaml`: 定义了应用的入口配置
- `kustomization.yaml`: Kustomize 配置文件，用于组织和自定义 Kubernetes 资源

## 常见问题

### 1. 镜像拉取失败

确保您已经创建了正确的 `regcred` Secret，并且在 deployment.yaml 中引用了它：

```yaml
imagePullSecrets:
- name: regcred
```

### 2. API 连接问题

检查 ConfigMap 中的 API URL 是否正确，确保后端服务在同一命名空间中可访问。

### 3. Ingress 配置问题

确保 Ingress 控制器已正确安装在集群中，并且 Ingress 资源配置正确。

### 4. 环境变量替换问题

如果 Nginx 配置中的环境变量没有被正确替换，可以考虑使用 envsubst 在容器启动时动态生成配置文件。
