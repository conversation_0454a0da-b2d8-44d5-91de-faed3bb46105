import React from 'react';
import ReactDOM from 'react-dom/client';

console.log('=== MAIN.JSX LOADING ===');
console.log('React version:', React.version);

// 直接在main.jsx中定义一个简单组件，避免导入问题
function SimpleApp() {
  console.log('SimpleApp component rendering');

  return React.createElement('div', {
    style: {
      padding: '20px',
      backgroundColor: 'lightblue',
      minHeight: '100vh',
      fontFamily: 'Arial, sans-serif'
    }
  }, [
    React.createElement('h1', {
      key: 'title',
      style: { color: 'red', textAlign: 'center' }
    }, '🚨 紧急调试页面 🚨'),

    React.createElement('div', {
      key: 'content',
      style: { textAlign: 'center', marginTop: '20px' }
    }, [
      React.createElement('p', { key: 'p1' }, '如果您能看到这个页面，说明React基本功能正常'),
      React.createElement('p', { key: 'p2' }, `当前时间: ${new Date().toLocaleString()}`),
      React.createElement('p', { key: 'p3' }, `React版本: ${React.version}`),
    ]),

    React.createElement('button', {
      key: 'button',
      onClick: () => {
        console.log('Button clicked!');
        alert('按钮功能正常！');
      },
      style: {
        padding: '15px 30px',
        fontSize: '16px',
        backgroundColor: 'green',
        color: 'white',
        border: 'none',
        borderRadius: '5px',
        cursor: 'pointer',
        display: 'block',
        margin: '20px auto'
      }
    }, '测试按钮')
  ]);
}

try {
  console.log('Getting root element...');
  const rootElement = document.getElementById('root');
  console.log('Root element:', rootElement);

  if (!rootElement) {
    throw new Error('Root element not found!');
  }

  console.log('Creating React root...');
  const root = ReactDOM.createRoot(rootElement);
  console.log('React root created successfully');

  console.log('Rendering app...');
  root.render(React.createElement(SimpleApp));
  console.log('=== APP RENDERED SUCCESSFULLY ===');

} catch (error) {
  console.error('=== ERROR IN MAIN.JSX ===', error);

  // 如果React渲染失败，直接操作DOM显示错误
  const rootElement = document.getElementById('root');
  if (rootElement) {
    rootElement.innerHTML = `
      <div style="padding: 20px; text-align: center; font-family: Arial, sans-serif; background: red; color: white;">
        <h1>🚨 应用启动失败 🚨</h1>
        <p>错误信息: ${error.message}</p>
        <p>错误堆栈: ${error.stack}</p>
        <button onclick="window.location.reload()" style="padding: 10px 20px; background: white; color: black; border: none; border-radius: 4px; cursor: pointer;">
          刷新页面
        </button>
      </div>
    `;
  } else {
    console.error('Root element not found for error display!');
  }
}
