// 创建一个通用的错误处理工具

// 解码消息中的Unicode转义序列
export const decodeMessage = (message) => {
  if (!message) return '';

  // 如果是对象，转换为字符串
  if (typeof message === 'object') {
    try {
      message = JSON.stringify(message);
    } catch (e) {
      console.error('转换对象到字符串失败:', e);
    }
  }

  try {
    // 尝试解析可能的 JSON 字符串
    if (typeof message === 'string' && (message.startsWith('{') || message.startsWith('['))) {
      try {
        const parsed = JSON.parse(message);
        if (parsed.error) {
          message = parsed.error;
        } else if (parsed.message) {
          message = parsed.message;
        } else if (typeof parsed === 'string') {
          message = parsed;
        }
      } catch (e) {
        // 忽略解析错误，继续处理原始字符串
      }
    }

    // 处理 Unicode 转义序列
    if (typeof message === 'string' && message.includes('\\u')) {
      // 使用 JSON.parse 处理 Unicode 转义
      message = JSON.parse(`"${message.replace(/"/g, '\\"')}"`);
    }

    return message;
  } catch (e) {
    console.error('解码消息失败:', e);
    return message; // 如果解码失败，返回原始消息
  }
};

// 处理API响应
export const handleApiResponse = async (response) => {
  if (!response.ok) {
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      try {
        const errorData = await response.json();
        throw new Error(decodeMessage(errorData.error || errorData.message || '操作失败'));
      } catch (jsonError) {
        // JSON解析失败，可能是收到了HTML错误页面
        const errorText = await response.text();
        if (errorText.includes('<!doctype') || errorText.includes('<html')) {
          throw new Error(`服务器返回了HTML页面而不是JSON数据，请检查服务器状态。状态码: ${response.status}`);
        } else {
          throw new Error(decodeMessage(errorText) || `操作失败，状态码: ${response.status}`);
        }
      }
    } else {
      const errorText = await response.text();
      if (errorText.includes('<!doctype') || errorText.includes('<html')) {
        throw new Error(`服务器返回了HTML页面而不是JSON数据，请检查服务器状态。状态码: ${response.status}`);
      } else {
        throw new Error(decodeMessage(errorText) || `操作失败，状态码: ${response.status}`);
      }
    }
  }

  // 检查响应内容长度
  const contentLength = response.headers.get('content-length');
  if (contentLength === '0' || contentLength === null) {
    return null; // 空响应
  }

  // 尝试解析JSON
  try {
    const text = await response.text();
    if (!text || text.trim() === '') {
      return null;
    }

    // 检查是否收到了HTML而不是JSON
    if (text.includes('<!doctype') || text.includes('<html')) {
      console.error('收到HTML响应而不是JSON:', text.substring(0, 200) + '...');
      throw new Error('服务器返回了HTML页面而不是JSON数据，请检查服务器状态');
    }

    return JSON.parse(text);
  } catch (error) {
    console.error('解析响应失败:', error);
    if (error.message.includes('服务器返回了HTML页面')) {
      throw error; // 重新抛出HTML错误
    }
    return null;
  }
};

